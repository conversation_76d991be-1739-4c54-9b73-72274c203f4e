{"staging": {"awscloudformation": {"AuthRoleName": "amplify-parenthingbackend-staging-8e0a0-authRole", "UnauthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-staging-8e0a0-unauthRole", "AuthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-staging-8e0a0-authRole", "Region": "ap-south-1", "DeploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "UnauthRoleName": "amplify-parenthingbackend-staging-8e0a0-unauthRole", "StackName": "amplify-parenthingbackend-staging-8e0a0", "StackId": "arn:aws:cloudformation:ap-south-1:590183703153:stack/amplify-parenthingbackend-staging-8e0a0/32064da0-fe40-11ee-920b-02e5ffaa23df", "AmplifyAppId": "d2eecjb5tdrjpn"}, "categories": {"function": {"user": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/user-2f6f652f6f3145324468-build.zip"}, "classes": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/classes-76644d4d2b3951595650-build.zip"}, "events": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/events-68635831753144744c73-build.zip"}, "onboarding": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/onboarding-2f38754f6765666a3964-build.zip"}, "business": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/business-32435157396369655447-build.zip"}, "superadmin": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/superadmin-2f6b754d71306672332b-build.zip"}, "appconfig": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/appconfig-48492f746249654f6a37-build.zip"}, "media": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/media-356d5872696a78476271-build.zip"}, "upload": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/upload-4f624f376e4935745947-build.zip"}, "notifications": {"deploymentBucketName": "amplify-parenthingbackend-staging-8e0a0-deployment", "s3Key": "amplify-builds/notifications-62533370744355514532-build.zip"}}, "api": {"parenthingstaging": {}}}}, "test": {"awscloudformation": {"AuthRoleName": "amplify-parenthingbackend-test-174404-authRole", "UnauthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-test-174404-unauthRole", "AuthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-test-174404-authRole", "Region": "ap-south-1", "DeploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "UnauthRoleName": "amplify-parenthingbackend-test-174404-unauthRole", "StackName": "amplify-parenthingbackend-test-174404", "StackId": "arn:aws:cloudformation:ap-south-1:590183703153:stack/amplify-parenthingbackend-test-174404/97fb89b0-2efe-11ef-998d-0212d730dc03", "AmplifyAppId": "d2eecjb5tdrjpn"}, "categories": {"function": {"appconfig": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/appconfig-48492f746249654f6a37-build.zip"}, "business": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/business-785650596e624672684b-build.zip"}, "classes": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/classes-674a4a32436754453565-build.zip"}, "events": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/events-4f50477a5a6e54366134-build.zip"}, "media": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/media-356d5872696a78476271-build.zip"}, "onboarding": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/onboarding-73374450345050704565-build.zip"}, "superadmin": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/superadmin-3776634e766544685235-build.zip"}, "upload": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/upload-54736559354b4332487a-build.zip"}, "user": {"deploymentBucketName": "amplify-parenthingbackend-test-174404-deployment", "s3Key": "amplify-builds/user-32776e75665731496d31-build.zip"}}, "api": {"parenthingstaging": {}}}}, "production": {"awscloudformation": {"AuthRoleName": "amplify-parenthingbackend-production-175512-authRole", "UnauthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-production-175512-unauthRole", "AuthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-production-175512-authRole", "Region": "ap-south-1", "DeploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "UnauthRoleName": "amplify-parenthingbackend-production-175512-unauthRole", "StackName": "amplify-parenthingbackend-production-175512", "StackId": "arn:aws:cloudformation:ap-south-1:590183703153:stack/amplify-parenthingbackend-production-175512/266eb630-2f00-11ef-a091-06b16d8f5cee", "AmplifyAppId": "d2eecjb5tdrjpn"}, "categories": {"function": {"appconfig": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/appconfig-48492f746249654f6a37-build.zip"}, "business": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/business-74556649756156443868-build.zip"}, "classes": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/classes-76644d4d2b3951595650-build.zip"}, "events": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/events-68635831753144744c73-build.zip"}, "media": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/media-356d5872696a78476271-build.zip"}, "onboarding": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/onboarding-616e63446d4c47396d72-build.zip"}, "superadmin": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/superadmin-67486170343454416765-build.zip"}, "upload": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/upload-4f624f376e4935745947-build.zip"}, "user": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/user-3249354d5a2f54744c38-build.zip"}, "notifications": {"deploymentBucketName": "amplify-parenthingbackend-production-175512-deployment", "s3Key": "amplify-builds/notifications-62533370744355514532-build.zip"}}, "api": {"parenthingstaging": {}}}}, "dev": {"awscloudformation": {"AuthRoleName": "amplify-parenthingbackend-dev-0e5bc-authRole", "UnauthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-dev-0e5bc-unauthRole", "AuthRoleArn": "arn:aws:iam::590183703153:role/amplify-parenthingbackend-dev-0e5bc-authRole", "Region": "ap-south-1", "DeploymentBucketName": "amplify-parenthingbackend-dev-0e5bc-deployment", "UnauthRoleName": "amplify-parenthingbackend-dev-0e5bc-unauthRole", "StackName": "amplify-parenthingbackend-dev-0e5bc", "StackId": "arn:aws:cloudformation:ap-south-1:590183703153:stack/amplify-parenthingbackend-dev-0e5bc/f8be9150-309e-11f0-a36e-02e09ab7a517", "AmplifyAppId": "d2eecjb5tdrjpn"}, "categories": {"function": {"appconfig": {}, "business": {}, "classes": {}, "events": {}, "media": {}, "notifications": {}, "onboarding": {}, "superadmin": {}, "upload": {}, "user": {}}}}}