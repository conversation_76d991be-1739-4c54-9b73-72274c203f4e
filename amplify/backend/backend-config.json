{"api": {"parenthingstaging": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "onboarding"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "appconfig"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "media"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "upload"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "superadmin"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "events"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "classes"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "user"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "business"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}}, "function": {"appconfig": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "business": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "classes": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "events": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "media": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "notifications": {"build": true, "dependsOn": [], "providerPlugin": "awscloudformation", "service": "Lambda"}, "onboarding": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "superadmin": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "upload": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "user": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_function_appconfig_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "appconfig"}]}, "AMPLIFY_function_appconfig_s3Key": {"usedBy": [{"category": "function", "resourceName": "appconfig"}]}, "AMPLIFY_function_business_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "business"}]}, "AMPLIFY_function_business_s3Key": {"usedBy": [{"category": "function", "resourceName": "business"}]}, "AMPLIFY_function_classes_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "classes"}]}, "AMPLIFY_function_classes_s3Key": {"usedBy": [{"category": "function", "resourceName": "classes"}]}, "AMPLIFY_function_events_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "events"}]}, "AMPLIFY_function_events_s3Key": {"usedBy": [{"category": "function", "resourceName": "events"}]}, "AMPLIFY_function_media_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "media"}]}, "AMPLIFY_function_media_s3Key": {"usedBy": [{"category": "function", "resourceName": "media"}]}, "AMPLIFY_function_notifications_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "notifications"}]}, "AMPLIFY_function_notifications_s3Key": {"usedBy": [{"category": "function", "resourceName": "notifications"}]}, "AMPLIFY_function_onboarding_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "onboarding"}]}, "AMPLIFY_function_onboarding_s3Key": {"usedBy": [{"category": "function", "resourceName": "onboarding"}]}, "AMPLIFY_function_superadmin_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "superadmin"}]}, "AMPLIFY_function_superadmin_s3Key": {"usedBy": [{"category": "function", "resourceName": "superadmin"}]}, "AMPLIFY_function_upload_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "upload"}]}, "AMPLIFY_function_upload_s3Key": {"usedBy": [{"category": "function", "resourceName": "upload"}]}, "AMPLIFY_function_user_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "user"}]}, "AMPLIFY_function_user_s3Key": {"usedBy": [{"category": "function", "resourceName": "user"}]}}}