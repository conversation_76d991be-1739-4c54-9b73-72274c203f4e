{"name": "upload", "version": "1.0.0", "description": "Lambda function generated by Amplify", "main": "index.js", "license": "Apache-2.0", "dependencies": {"@aws-sdk/client-s3": "^3.577.0", "@aws-sdk/s3-request-presigner": "^3.577.0", "aws-serverless-express": "^3.3.5", "body-parser": "^1.17.1", "express": "^4.15.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.9.7", "uuid": "^10.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/aws-lambda": "^8.10.92"}}