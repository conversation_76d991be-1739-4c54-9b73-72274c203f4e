const {
  uploadService,
  deleteService,
  exportService,
} = require("../service/uploadService");
class uploadController {
  static async media(req, res) {
    try {
      const payload = req.body;

      const requiredParams = (param, message) => {
        if (param == null) {
          return message;
        }
      };

      const errors = [
        requiredParams(payload.key, "key is required!"),
        requiredParams(payload.ContentType, "ContentType is required!"),
        requiredParams(payload.image_usage, "image_usage is required!"),
        payload.parent_id == null &&
          payload.business_id == null &&
          payload.admin_id == null &&
          "Either parent_id, business_id or admin_id is required!",
      ].filter(Boolean);

      if (errors.length) {
        return res
          .status(400)
          .send({ success: false, msg: errors.join(", "), data: {} });
      }

      const result = await uploadService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "PreSigned URL generated to Upload",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Failed to generate presigned URL. Please check the request and try again.",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: "An error occurred while processing your request.",
        error: error.message,
      });
    }
  }

  static async delete(req, res) {
    try {
      const payload = req.body;

      const result = await deleteService(payload);
      console.log("result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Image deleted",
          data: {},
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Failed to delete data ",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async exporttoExcel(req, res) {
    try {
      const payload = req.body;

      const result = await exportService(payload);
      console.log("result", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Data Exported",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Failed to export data ",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
}

module.exports = uploadController;
