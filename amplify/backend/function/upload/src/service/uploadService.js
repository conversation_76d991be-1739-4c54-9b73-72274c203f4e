const {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} = require("@aws-sdk/client-s3");
const db = require("../config/db");
const mime = require("mime-types");

const xlsx = require("xlsx");
const { v4: uuidv4 } = require("uuid");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");

const s3Client = new S3Client({
  region: "ap-south-1",
  credentials: {
    accessKeyId: "********************",
    secretAccessKey: "sscCud7RMixFfuCqXLey0uKFjrl0oDxvRCeAf9CP",
  },
});

// async function putObject(payload) {
//   console.log("Payload");
//   console.log("inside putobject:>>", payload?.key);
//   console.log("inside putobject:>>", payload?.ContentType);
//   // const params = {
//   //   Bucket: "profilemedia",
//   //   Key: payload?.key,
//   //   ContentType: mime.lookup(payload.key),
//   //   // ContentType: payload?.ContentType,
//   // };
//   const isPdf = payload?.key.endsWith(".pdf");
//   let contentType = isPdf ? "application/pdf" : payload.ContentType;
//   const params = {
//     Bucket: "profilemedia",
//     Key: payload?.key,
//     ContentType: contentType,
//   };
//   console.log("params::>>", params);

//   const url = await getSignedUrl(s3Client, new PutObjectCommand(params));
//   console.log("url::>>", url);
//   return url;
// }

// const uploadService = async (payload, res) => {
//   try {
//     console.log("Upload::>>", payload);
//     console.log("key", payload?.key);
//     console.log("ContentType", payload?.ContentType);
//     const procedure = `CALL spAddMedia(?)`;
//     const [row] = await db.promise().query(procedure, JSON.stringify(payload));
//     console.log("Row::>>", row);
//     const preSignedUrl = await putObject(payload);
//     console.log("presignedurl::>");
//     return preSignedUrl;
//   } catch (error) {
//     console.error("error in app", error);
//     return null;
//   }
// };

// async function putObject(payload) {
//   console.log("Payload");
//   console.log("inside putobject:>>", payload?.key);
//   console.log("inside putobject:>>", payload?.ContentType);
//   const params = {
//     Bucket: "profilemedia",
//     Key: payload?.key,
//     ContentType: payload?.ContentType,
//   };
//   const url = await getSignedUrl(s3Client, new PutObjectCommand(params));
//   console.log("url::>>", url);
//   return url;
// }

// const uploadService = async (payload, res) => {
//   try {
//     console.log("Upload::>>", payload);
//     console.log("key", payload?.key);
//     console.log("ContentType", payload?.ContentType);
//     const procedure = `CALL spAddMedia(?)`;
//     const [row] = await db.promise().query(procedure, JSON.stringify(payload));
//     console.log("Row::>>", row);
//     const preSignedUrl = await putObject(payload);
//     console.log("presignedurl::>");
//     return preSignedUrl;
//   } catch (error) {
//     console.error("error in app", error);
//     return null;
//   }
// };

async function generatePublicURL(key) {
  const url = `https://profilemedia.s3.ap-south-1.amazonaws.com/${key}`;
  return url;
}

async function putObject(payload) {
  console.log("Payload");
  console.log("inside putobject:>>", payload?.key);
  console.log("inside putobject:>>", payload?.ContentType);
  const params = {
    Bucket: "profilemedia",
    Key: payload?.key,
    ContentType: payload?.ContentType,
  };
  const url = await getSignedUrl(s3Client, new PutObjectCommand(params));
  console.log("url::>>", url);
  return url;
}

const uploadService = async (payload, res) => {
  try {
    console.log("Upload::>>", payload);
    console.log("key", payload?.key);
    console.log("ContentType", payload?.ContentType);
    const procedure = `CALL spAddMedia(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);
    const preSignedUrl = await putObject(payload);
    console.log("presignedurl::>", preSignedUrl);
    const downloadURL = await generatePublicURL(payload.key);
    console.log("Download URL:", downloadURL);
    return { preSignedUrl, downloadURL };
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};

const deleteService = async (payload) => {
  try {
    const key = payload.key;
    const params = {
      Bucket: "profilemedia",
      Key: key,
    };
    const deleteCommand = new DeleteObjectCommand(params);
    const response = await s3Client.send(deleteCommand);
    console.log("response", response);
    return ` ${key} deleted successfully`;
  } catch (error) {
    console.log("error in app", error);
    return null;
  }
};

const exportService = async (payload) => {
  try {
    console.log("Upload::>>", payload);

    const procedure = `CALL spGetKycList(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);

    const worksheet = xlsx.utils.json_to_sheet(row[0]);
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, worksheet, "Data");

    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const random_name = uuidv4();
    console.log("random_name", random_name);
    const s3Params = {
      Bucket: "parenthing",
      Key: `Exports/${random_name}.xlsx`,
      Body: excelBuffer,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ACL: "public-read",
    };

    const response = await s3Client.send(new PutObjectCommand(s3Params));
    console.log("response", response);
    const file_name = `https://parenthing.s3.ap-south-1.amazonaws.com/Exports/${random_name}.xlsx`;
    if (response) {
      return file_name;
    } else {
      return null;
    }
  } catch (error) {
    console.error("error in app", error);
    return null;
  }
};
module.exports = { uploadService, deleteService, exportService };
