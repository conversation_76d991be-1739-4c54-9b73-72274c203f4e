const db = require("../config/db");
const axios = require("axios");
const nodemailer = require("nodemailer");
const AWS = require("aws-sdk");
require("dotenv").config();
require("aws-sdk/lib/maintenance_mode_message").suppress = true;
const { MailerSend, EmailParams, Sender, Recipient } = require("mailersend");
//tech.bitroot

// const MailerSendAPIKEY =
//   "mlsn.385f83ba05db5995247c4efc1a930fa502576e5fbec7e2d943ae48d0f15894f8";
//parenthing APIKey
const MailerSendAPIKEY =
  "mlsn.172cd30cc2cdf2a287d287aebf1402581cecfdb00ed6a37bf13de4a9d69668d6";

const mailerSend = new MailerSend({
  apiKey: MailerSendAPIKEY,
});

//tech.bitroot
// const sentFrom = new Sender(
//   "<EMAIL>",
//   "Tech Bitroot"
// );
const sentFrom = new Sender("<EMAIL>", "Parenthing Support");
const SES_CONFIG = {
  accessKeyId: process.env.AWS_ACCESS_KEY,
  secretAccessKey: "10FOwStLpGhyS4iylRqkE3QX5/KlrezdtaVhEgBg",
  region: process.env.AWS_SES_REGION,
};
const apiSecret = "983fba9be5f14589880bcf4bb2ab2f85";
const apiKey = "6633779114dfbdf22e3efcbb";
const AWS_SES = new AWS.SES(SES_CONFIG);
const authkey = `420745AQofv3Fn66332257P1`;

const config = {
  headers: {
    Accept: "application/json",
    authkey: `${authkey}`,
    "Content-Type": "application/json",
  },
};

// const transporter = nodemailer.createTransport({
//   service: "gmail",
//   port: 465,
//   secure: true,
//   auth: {
//     // TODO: replace `user` and `pass` values from <https://forwardemail.net>
//     user: "<EMAIL>",
//     pass: "mmwu irck yrhv svzm",
//   },
// });
const transporter = nodemailer.createTransport({
  service: "gmail",
  port: 465,
  secure: true,
  auth: {
    // TODO: replace `user` and `pass` values from <https://forwardemail.net>
    user: "<EMAIL>",
    pass: "mmwu irck yrhv svzm",
  },
});

const sendEmail = (mailOptions) => {
  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info);
      }
    });
  });
};

// msg91 config
// const MSG91_AUTH_KEY = '420277AMzpqQdDmSNi68271449P1'; //parentease account 
const MSG91_AUTH_KEY = '420745AQofv3Fn66332257P1';  // parentease2 account
const MSG91_CONFIG = {
  headers: {
    'authkey': MSG91_AUTH_KEY,
    'content-type': 'application/json',
    'accept': 'application/json'
  }
};

const sendWhatsAppMessage = async (payload) => {
  const { name, phone, type } = payload;
  console.log("WhatsApp payload::>>", payload);

  try {
    // Configure template based on type
    let templateName;
    switch(type) {
      case 'query':
        templateName = 'query_recieved';
        break;
      case 'business':
        templateName = 'waitlist_business';
        break;
      case 'parent':
        templateName = 'waitlist_parent';
        break;
      default:
        throw new Error('Invalid user type');
    }
    
    const whatsappData = {
      integrated_number: "************",
      content_type: "template",
      payload: {
        type: "template",
        template: {
          name: templateName,
          language: {
            code: type === 'business' ? "en_us" : "en",
            policy: "deterministic"
          },
          to_and_components: [
            {
              to: [phone],
              components: {} // No variables needed for any template
            }
          ]
        },
        messaging_product: "whatsapp"
      }
    };

    const options = {
      method: 'POST',
      url: 'https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
      headers: {
        authkey: MSG91_AUTH_KEY,
        'content-type': 'application/json',
        accept: 'application/json'
      },
      data: whatsappData
    };

    const response = await axios(options);
    console.log("WhatsApp API Response:", response.data);

    if (response.data.status === "success") {
      return { status: true, msg: "WhatsApp message sent successfully", data: response.data };
    } else {
      console.error("WhatsApp send failed:", response.data);
      return { status: false, msg: "Failed to send WhatsApp message", data: response.data };
    }

  } catch (error) {
    console.error(
      "WhatsApp Error:",
      error.response ? error.response.data : error.message
    );
    return { status: false, msg: "WhatsApp API error", error: error.message };
  }
};

// const sendWhatsAppMessage = async (whatsappPayload) => {
//   console.log("whats app payload::>>", whatsappPayload);
//   try {
//     const options = {
//       method: "POST",
//       url: `https://server.gallabox.com/devapi/messages/whatsapp`,
//       headers: {
//         apiSecret: apiSecret,
//         apiKey: apiKey,
//         "Content-Type": "application/json",
//       },
//       data: whatsappPayload, // No need to stringify here
//     };
//     const result = await axios(options);
//     console.log("result", result.data);

//     if (result.data.status === "ACCEPTED") {
//       return { status: true, msg: result.data.message, data: {} };
//     } else {
//       return null;
//     }
//   } catch (error) {
//     console.error(
//       "Error sending message:",
//       error.response ? error.response.data : error.message
//     );
//     return null;
//   }
// };

const categoryListService = async () => {
  try {
    const procedure = `CALL spGetCategoryWithSubcategories()`;
    const [row] = await db.promise().query(procedure);

    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const onboardingDataService = async (payload) => {
  try {
    const procedure = `CALL spOnboardingData(?)`;
    const [[[row]]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    // await sendEmail(row.EmailId);
    console.log("Row", row.EmailId);
    const emailid = row.EmailId;
    const Businessname = row.BusinessName;
    const mobileNO = row.PersonOfContactPhoneNumber;
    const sendMail = payload.notify_on_launch;
    console.log("SEND mail::>>", payload.notify_on_launch);
    // const sendEmails = async () => {
    //   let params = {
    //     Source: "<EMAIL>",
    //     Destination: {
    //       ToAddresses: [emailid],
    //     },
    //     ReplyToAddresses: [],
    //     Message: {
    //       Body: {
    //         Html: {
    //           Charset: "UTF-8",
    //           Data: `<p>Thank you for submitting the form! We've received your information and will be in touch shortly. - Team Parenthing</p>`,
    //         },
    //         Text: {
    //           Charset: "UTF-8",
    //           Data: "Thank you for submitting the form! We've received your information and will be in touch shortly. - Team Parenthing",
    //         },
    //       },
    //       Subject: {
    //         Charset: "UTF-8",
    //         Data: "An announcement from bitroot",
    //       },
    //     },
    //   };

    //   // console.log();
    //   try {
    //     console.log("Params", params);
    //     const res = await AWS_SES.sendEmail(params).promise();
    //     console.log("Email has been sent!", res);
    //   } catch (error) {
    //     console.error(error);
    //   }
    // };
    // await sendEmails();

    // const sendEmail = () => {
    //   //  Setup email data
    //   // console.log("To:-", to);
    //   const mailOptions = {
    //     from: "<EMAIL>", // Sender address
    //     to: "<EMAIL>", // List of receivers
    //     subject: " Onboarding Successfull", // Subject line
    //     text: "Thank you for submitting the form! We've received your information and will be in touch shortly. - Team Parenthing",
    //     html: "<p>Thank you for submitting the form! We've received your information and will be in touch shortly. - Team Parenthing</p>", // HTML body
    //   };

    //   // Send email using transporter
    //   transporter.sendMail(mailOptions, (error, info) => {
    //     if (error) {
    //       console.log("Error occurred while sending email:", error);
    //     } else {
    //       console.log("Email sent:", info.response);
    //     }
    //   });
    // };
    // await sendEmail();
    // const mailOptions = {
    //   from: "<EMAIL>", // Sender address
    //   to: emailid, // List of receivers
    //   subject: " Onboarding Successful", // Subject line
    //   text: "Thank you for submitting the form! We've received your information and will be in touch shortly. - Team Parenthing",
    //   html: `<p>Thank you ${Businessname} for submitting the form! We've received your information and will be in touch shortly. - Team Parenthing</p>`, // HTML body
    // };
    const whatsappPayload = {
      channelId: "661cc747b1978ef4ff47b3d4",
      channelType: "whatsapp",
      recipient: { name: `${Businessname}`, phone: `91${mobileNO}` },
      whatsapp: {
        type: "template",
        template: { templateName: "welcome_onboard_final_clone" },
      },
    };
    const data = {
      recipients: [
        {
          to: [
            {
              name: `${Businessname}`,
              email: `${emailid}`,
            },
          ],
          variables: {
            VendorName: `${Businessname}`,
          },
        },
      ],
      from: {
        name: "Parenthing Support",
        email: "<EMAIL>",
      },
      domain: "parenthingapp.in",
      template_id: "welcome_business_2",
    };
    if (sendMail === 1 && emailid) {
      await axios
        .post("https://control.msg91.com/api/v5/email/send", data, config)
        .then((response) => {
          console.log(response.data);
        })
        .catch((error) => {
          console.error("Error:", error.response.data);
        });
    } else {
      console.log("Mail unchecked");
    }
    // const recipients = [new Recipient(`${emailid}`, `${Businessname}`)];

    // const personalization = [
    //   {
    //     email: `${emailid}`,
    //     data: {
    //       name: `${Businessname}`,
    //     },
    //   },
    // ];

    // const emailParams = new EmailParams()
    //   .setFrom(sentFrom)
    //   .setTo(recipients)
    //   .setReplyTo(sentFrom)
    //   .setSubject("Onboarding Successful")
    //   .setTemplateId("3vz9dle38vpgkj50")
    //   .setPersonalization(personalization)
    //   .setHtml(
    //     "<p>Thank You for your submission on Parenthing. Our Business team will reach out to you at the earliest. <br>Team Parenthing.</p>"
    //   )

    //   .setText("This is the text content");

    // mailerSend.email
    //   .send(emailParams)
    //   .then(() => console.log("Email sent successfully"))
    //   .catch((error) => console.error("Error sending email:", error));
    if (sendMail === 1 && mobileNO) {
      await sendWhatsAppMessage(whatsappPayload);
      // await sendEmail(mailOptions);  Uncomment to use Pawan's Mail via nodemailer
    } else {
      console.log("Mail unchecked");
    }
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const tableDataService = async (payload) => {
  try {
    const procedure = `CALL spTableData()`;
    const [row] = await db.promise().query(procedure);
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const getTeamListService = async (payload) => {
  try {
    const procedure = `CALL spTeamList()`;
    const [row] = await db.promise().query(procedure);
    console.log("Row", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("error in app", error);
  }
};

const joinWaitlistService = async (payload) => {
  try {
    console.log('Starting joinWaitlist service with payload:', payload);
    const { name, mobile, type, message } = payload;

    // Input validation
    if (!name || !mobile || !type) {
      console.log('Validation failed:', { name, mobile, type });
      throw new Error('Name, mobile and type are required fields');
    }

    // Check for existing user
    const [existingUser] = await db.promise().query(
      'SELECT * FROM userswaitlist WHERE mobile = ?',
      [mobile]
    );
    console.log('Existing user check result:', existingUser);

    let result;
    let shouldSendNotification = false;

    if (existingUser && existingUser.length > 0) {
      const currentUser = existingUser[0];
      console.log('Found existing user:', currentUser);

      if (type === 'query') {
        await db.promise().query(
          'UPDATE userswaitlist SET message = ? WHERE mobile = ?',
          [message, mobile]
        );
        result = { status: "updated", message: "Query message updated" };
        shouldSendNotification = true;
      } else if (currentUser.type === 'query' && (type === 'business' || type === 'parent')) {
        await db.promise().query(
          'UPDATE userswaitlist SET type = ? WHERE mobile = ?',
          [type, mobile]
        );
        result = { status: "updated", message: "Entry updated from query to " + type };
        shouldSendNotification = true;
      } else {
        result = { status: "exists", message: "Already registered" };
      }
    } else {
      // New entry
      await db.promise().query(
        'INSERT INTO userswaitlist (name, mobile, type, message) VALUES (?, ?, ?, ?)',
        [name, mobile, type, message]
      );
      result = { status: "created", message: "Successfully joined waitlist" };
      shouldSendNotification = true;
    }

    // Send notifications if needed
    if (shouldSendNotification) {
      console.log('Sending notifications...');
      
      let notificationResults = {
        sms: false,
        whatsapp: false
      };
      
      // 1. Send SMS notification
      let smsTemplateId;
      switch (type) {
        case 'business':
          smsTemplateId = '682c5af4d6fc0514ab6df2e3';
          break;
        case 'query':
          smsTemplateId = '682c596ed6fc05170c5c8f12';
          break;
        case 'parent':
          smsTemplateId = '682c75acd6fc057d0f269313';
          break;
      }

      const smsData = {
        flow_id: smsTemplateId,
        recipients: [
          {
            mobiles: `91${mobile}`,
            var: name
          }
        ]
      };

      try {
        const smsResponse = await axios.post(
          'https://control.msg91.com/api/v5/flow/',
          smsData,
          MSG91_CONFIG
        );
        console.log('SMS Response:', smsResponse.data);
        notificationResults.sms = true;
      } catch (smsError) {
        console.error('SMS Notification failed:', smsError.response?.data || smsError.message);
        // Continue execution even if SMS fails
      }

      // 2. Send WhatsApp notification
      const whatsappPayload = {
        name: name,
        phone: `91${mobile}`, 
        type: type
      };

      try {
        const whatsappResult = await sendWhatsAppMessage(whatsappPayload);
        console.log('WhatsApp notification result:', whatsappResult);
        notificationResults.whatsapp = whatsappResult?.status || false;
      } catch (whatsappError) {
        console.error('WhatsApp Notification failed:', whatsappError);
        // Continue execution even if WhatsApp fails
      }

      // Add notification status to result
      // result.notifications = notificationResults;
    }

    console.log('Returning result:', result);
    return result;

  } catch (error) {
    console.error('JoinWaitlist Service Error:', error);
    throw error;
  }
};

module.exports = {
  categoryListService,
  getTeamListService,
  onboardingDataService,
  tableDataService,
  joinWaitlistService,
};
