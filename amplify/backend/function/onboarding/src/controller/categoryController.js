const db = require("../config/db");
const axios = require("axios");

const {
  categoryListService,
  onboardingDataService,
  tableDataService,
  getTeamListService,
  joinWaitlistService,
} = require("../service/categoryService");
const { json } = require("body-parser");

class CategoryController {
  static async categoryList(req, res) {
    try {
      const [result] = await categoryListService();
      console.log("result::>>");
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Category List",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Check your input.",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async onboardingData(req, res) {
    try {
      const payload = req.body;
      console.log("payload::>>", payload);
      const result = await onboardingDataService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Data saved",
          data: {},
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Check your input.",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async tableData(req, res) {
    try {
      const payload = req.body;
      const [result] = await tableDataService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Business Data",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Error in fetching Data.",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async getTeamList(req, res) {
    try {
      const payload = req.body;
      const [result] = await getTeamListService(payload);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Team List",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "Error in fetching Data.",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async joinWaitlist(req, res) {
    try {
      const payload = req.body;
      
      // Validate required fields
      if (!payload.name || !payload.mobile || !payload.type) {
        return res.status(400).send({
          success: false,
          msg: "Name, mobile and type are required",
          data: {}
        });
      }

      // Validate type
      if (!['business', 'parent', 'query'].includes(payload.type)) {
        return res.status(400).send({
          success: false,
          msg: "Invalid type. Must be business, parent or query",
          data: {}
        });
      }

      // Validate message for query type
      if (payload.type === 'query' && !payload.message) {
        return res.status(400).send({
          success: false,
          msg: "Message is required for query type",
          data: {}
        });
      }

      const result = await joinWaitlistService(payload);

      return res.status(200).send({
        success: true,
        msg: result.message,
        data: result
      });

    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {}
      });
    }
  }

}

module.exports = CategoryController;
