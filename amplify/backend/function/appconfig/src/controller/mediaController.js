const db = require("../config/db");
const { v4: uuidv4 } = require("uuid");
const AWS = require("aws-sdk");
const s3 = new AWS.S3();
const bucket = "parenthing";
const fs = require("fs");

// const creds = {
//   accessKeyId: "jvfjbfjbvf",
//   secretAccessKey: "fdhvhfvhf",
//   region: "ap-south-1",
// };

class MediaController {
  static async upload(req, res) {
    const file = req.body;
    let id = uuidv4();
    const buffer = Buffer.from(file, "base64");
    fs.writeFile(`/tmp/${id}.jpg`, buffer, function (err) {
      if (err) {
        callbackify(null, {
          success: false,
          msg: `Something went wrong`,
          data: {},
        });
      } else {
        let params = {
          ACL: "public-read",
          Bucket: bucket,
          Body: fs.createReadStream(`/tmp/${id}.jpg`),
          Key: `profile/${id}.jpg`,
          ContentType: `image/jpeg`,
        };

        s3.upload(params, (error, data) => {
          fs.unlinkSync(`/tmp/${id}.jpg`);
          if (error) {
            callback(null, {
              success: false,
              msg: `Something went wrong`,
              data: [],
            });
          }

          callback(null, {
            success: true,
            msg: `Uploaded to s3!`,
            data: { ...data },
          });
        });
      }
    });
  }
}

module.exports = MediaController;
