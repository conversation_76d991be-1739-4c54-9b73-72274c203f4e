/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
const AWS = require("aws-sdk");
const multiparty = require("multiparty");
const fs = require("fs"); // Require fs module
const s3 = new AWS.S3();

exports.handler = async (event) => {
  console.log("event::>>", event);
  const form = new multiparty.Form();

  const result = await new Promise((resolve, reject) => {
    form.parse(event, (err, fields, files) => {
      if (err) {
        console.log("error::>>", err);
        reject(err);
      } else {
        resolve({ fields, files });
      }
    });
  });
  console.log("Result::>>", result);

  const file = result.files.file[0];
  console.log("File path::>>", file.path);

  // Parse Content-Type to get file extension
  const contentType = file.headers["content-type"];
  console.log("ContentType", contentType);
  const fileExtension = contentType.split("/")[1];
  console.log("File extension", fileExtension);

  const fileStream = fs.createReadStream(file.path);
  console.log("fileStream", fileStream);

  const uploadParams = {
    Bucket: "profilemedia",
    Key: `${Date.now()}.${fileExtension}`, // Change file name to include timestamp and extension
    Body: fileStream,
  };

  try {
    const uploadResult = await s3.upload(uploadParams).promise();
    console.log("Upload result", uploadResult);
    return {
      statusCode: 200,
      body: JSON.stringify({ url: uploadResult.Location }),
    };
  } catch (error) {
    console.error("Upload error", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: "Failed to upload file" }),
    };
  }
};
