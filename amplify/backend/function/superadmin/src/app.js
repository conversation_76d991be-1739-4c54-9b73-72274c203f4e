/*
Copyright 2017 - 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License"). You may not use this file except in compliance with the License. A copy of the License is located at
    http://aws.amazon.com/apache2.0/
or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/

const express = require("express");
const bodyParser = require("body-parser");
const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
const SuperAdminController = require("./controller/SuperAdminController");
const Authorization = require("./auth/tokenValidator");

// declare a new express app
const app = express();
app.use(bodyParser.json());
app.use(awsServerlessExpressMiddleware.eventContext());

// Enable CORS for all methods
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "*");
  next();
});

/**********************
 * Example get method *
 **********************/

app.get("/dashboard/superadmin/home", SuperAdminController.home);

app.get(
  "/dashboard/superadmin/getRolesList",
  SuperAdminController.getRolesList
);

app.get("/dashboard/superadmin", function (req, res) {
  // Add your code here
  res.json({ success: "get call succeed!", url: req.url });
});

app.get("/dashboard/superadmin/*", function (req, res) {
  // Add your code here
  res.json({ success: "get call succeed!", url: req.url });
});

/****************************
 * Example post method *
 ****************************/
app.post("/dashboard/superadmin/verifyKyc", SuperAdminController.verifyKyc);

app.post(
  "/dashboard/superadmin/suspendParent",
  SuperAdminController.suspendParent
);

app.post(
  "/dashboard/superadmin/setAdminRoles",
  SuperAdminController.setAdminRoles
);

//app.post("/dashboard/superadmin/homepageAnalytics", SuperAdminController.homePageAnalytics);

app.post("/dashboard/superadmin/kycList", SuperAdminController.kycList);

app.post(
  "/dashboard/superadmin/kycRequestById",
  SuperAdminController.kycRequestById
);

app.post(
  "/dashboard/superadmin/getBusinessById",
  SuperAdminController.getBusinessById
);

app.post(
  "/dashboard/superadmin/businessList",
  SuperAdminController.getBusinessList
);

app.post("/dashboard/superadmin/eventList", SuperAdminController.eventList);

app.post(
  "/dashboard/superadmin/eventRequestById",
  SuperAdminController.eventRequestById
);

app.post(
  "/dashboard/superadmin/classRequestById",
  SuperAdminController.classRequestById
);

app.post("/dashboard/superadmin/classList", SuperAdminController.classList);

app.post("/dashboard/superadmin/reportList", SuperAdminController.reportList);
app.post("/dashboard/superadmin/parentsList", SuperAdminController.parentsList);
// app.post("/dashboard/superadmin/suspendparent",SuperAdminController.);
app.post(
  "/dashboard/superadmin/inactiveParents",
  SuperAdminController.inactiveParents
);

//This is for homepage analytics:-
app.post("/dashboard/superadmin/analytics", SuperAdminController.analytics);

//This is for analytics module:-
app.post(
  "/dashboard/superadmin/moduleAnalytics",
  SuperAdminController.analyticsModule
);

app.post(
  "/dashboard/superadmin/setHomepageImage",
  SuperAdminController.setHomepageImage
);

app.post(
  "/dashboard/superadmin/reportedEventById",
  SuperAdminController.reportedEventById
);
app.post(
  "/dashboard/superadmin/reportedClassById",
  SuperAdminController.reportedClassById
);
app.post(
  "/dashboard/superadmin/reportedParentslist",
  SuperAdminController.reportedParentslist
);

app.post(
  "/dashboard/superadmin/getParentById",
  SuperAdminController.parentById
);
app.post(
  "/dashboard/superadmin/getAdminById",
  SuperAdminController.getAdminById
);

app.post(
  "/dashboard/superadmin/disableAccount",
  SuperAdminController.disableAccount
);

app.post(
  "/dashboard/superadmin/approveRejectEvents",
  SuperAdminController.approveRejectEvents
);

app.post(
  "/dashboard/superadmin/approveRejectClass",
  SuperAdminController.approveRejectClass
);

app.post(
  "/dashboard/superadmin/approveRejectReported",
  SuperAdminController.approveRejectReported
);

app.post(
  "/dashboard/superadmin/approveRejectReportedParent",
  SuperAdminController.approveRejectReportedParent
);

app.post(
  "/dashboard/superadmin/editAdminById",
  SuperAdminController.editAdminById
);

app.post(
  "/dashboard/superadmin/deleteAdminById",
  SuperAdminController.deleteAdminById
);

app.post(
  "/dashboard/superadmin/getBusinessLocation",
  SuperAdminController.getBusinessLocation
);

//this is updated code................D

app.post("/dashboard/superadmin/adminList", SuperAdminController.adminList);
// app.post("/dashboard/superadmin/generateotp", SuperAdminController.generateotp);
app.post("/dashboard/superadmin", function (req, res) {
  // Add your code here
  res.json({ success: "post call succeed!", url: req.url, body: req.body });
});

app.post("/dashboard/superadmin/*", function (req, res) {
  // Add your code here
  res.json({ success: "post call succeed!", url: req.url, body: req.body });
});

/****************************
 * Example put method *
 ****************************/

app.put("/dashboard/superadmin", function (req, res) {
  // Add your code here
  res.json({ success: "put call succeed!", url: req.url, body: req.body });
});

app.put("/dashboard/superadmin/*", function (req, res) {
  // Add your code here
  res.json({ success: "put call succeed!", url: req.url, body: req.body });
});

/****************************
 * Example delete method *
 ****************************/

app.delete("/dashboard/superadmin", function (req, res) {
  // Add your code here
  res.json({ success: "delete call succeed!", url: req.url });
});

app.delete("/dashboard/superadmin/*", function (req, res) {
  // Add your code here
  res.json({ success: "delete call succeed!", url: req.url });
});

app.listen(3000, function () {
  console.log("App started");
});

// Export the app object. When executing the application local this does nothing. However,
// to port it to AWS Lambda we will create a wrapper around that will load the app from
// this file
module.exports = app;
