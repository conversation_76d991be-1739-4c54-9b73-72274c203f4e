const db = require("../config/db");

const { requiredParams } = require("../utility/requiredCheck");
const axios = require("axios");
const {
  disableAccountService,
  exportReportedParentsListService,
  exportInactiveParentsService,
  exportParentListService,
  exportReportListService,
  exportClassListService,
  exportBusinessListService,
  exportEventListService,
  exportKycListService,
  adminListService,
  analyticsService,
  parentsListService,
  reportedEventByIdService,
  reportedClassByIdService,
  classRequestByIdService,
  kycRequestByIdService,
  eventRequestByIdService,
  getBusinessByIdService,
  reportListService,
  classListService,
  eventListService,
  verifyKycService,
  sendOtpService,
  getBusinessListService,
  kycListService,
  homeService,
  inactiveParentsService,
  reportedParentsListService,
  getParentByIdService,
  suspendparentService,
  getAdminByIdService,
  approveRejectEventsService,
  approveRejectClassService,
  editAdminByIdService,
  setHomepageImageService,
  analyticsModuleService,
  approveRejectReportedService,
  approveRejectReportedParentService,
  getBusinessLocationService,
  getRolesListService,
  setAdminRolesService,
  deleteAdminByIdService,
  getFirebaseNotification,
} = require("../service/SuperAdminService");

class SuperAdminController {
  static async sendOtp(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { mobile, name } = payload;
    const otp = Math.floor(1000 + Math.random() * 9000).toString();
    console.log("otp", otp);
    // const encryptedOtp = otp;
    const encryptedOtp = await bcrypt.hash(otp, 10);
    console.log("EncryptedOTP", encryptedOtp);
    const userPayload = { ...payload, otp: encryptedOtp, type: "temp" };
    if (mobile == null || mobile == undefined)
      return requiredParams(res, "mobile is required!", 406);

    if (!/^\d{10}$/.test(mobile)) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number must be 10 digits long",
        data: {},
      });
    }

    const isverified = `select isverified from OTP where mobile = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, mobile);
    console.log("verified Check:>>", verifiedcheck);
    // if (verifiedcheck?.isverified === 1)
    //   return requiredParams(res, "User already verified", 406, verifiedcheck);
    let result;
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // await getWhatsappService({
      //   // user_id: isExist.temp_id,
      //   type: "temp",
      //   data: {
      //     message: `Your Otp is ${otp}.`,
      //     OTP: otp,
      //     mobile: mobile,
      //   },
      //   template_name: "parenthing_otp_testv3",
      //   body_values: {
      //     OTP: otp,
      //   },
      // });
      if (verifiedcheck && verifiedcheck.isverified === 1) {
        // await verifiedMSG91Service({
        //   mobile: mobile,
        //   otp: otp,
        // });
      } else if (
        verifiedcheck?.isverified == null ||
        verifiedcheck?.isverified == undefined ||
        verifiedcheck?.isverified == 0
      ) {
        console.log("else field");
        // await getMSG91Service({
        //   mobile: mobile,
        //   otp: otp,
        // });
      }

      // console.log("User already exist!", isExist.user_id);
      const updated = await sendOtpService(userPayload);
      console.log("updateOtp", updated);

      return res.status(200).send({
        success: true,
        msg: "Otp sent successfully.",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async verifyKyc(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id, status, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);

    if (status == null || status == undefined)
      return requiredParams(res, "status is required!", 406);

    const isverified = `select count(*) as status from business where business_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, business_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "business_id doesn't exists", 406);
    }

    const request_check = `select count(*) as status from kyc_requests where business_id = ?`;
    const [[checkRequest]] = await db
      .promise()
      .query(request_check, business_id);
    console.log("verified Check:>>", checkRequest);

    if (checkRequest.status === 0) {
      return requiredParams(
        res,
        "No KYC request available from that business",
        406
      );
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const updated = await verifyKycService(payload);
      console.log("updateOtp", updated);

      return res.status(200).send({
        success: true,
        msg: "Kyc status updated successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async suspendParent(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, type, reason, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);
    if (type == null || type == undefined)
      return requiredParams(res, "type is required!", 406);

    const isverified = `select count(*) as status from Parents where parent_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, parent_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "parent_id doesn't exists", 406);
    }

    // if (checkRequest.status === 0) {
    //   return requiredParams(
    //     res,
    //     "No KYC request available from that business",
    //     406
    //   );
    // }

    try {
      const updated = await suspendparentService(payload);
      console.log("USER", updated);

      return res.status(200).send({
        success: true,
        msg: "Parent suspended successfully",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async approveRejectEvents(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { event_id, type, reason, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (event_id == null || event_id == undefined)
      return requiredParams(res, "event_id is required!", 406);
    if (type == null || type == undefined)
      return requiredParams(res, "type is required!", 406);

    const isverified = `select count(*) as status from events where event_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, event_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "event_id doesn't exists", 406);
    }

    try {
      const updated = await approveRejectEventsService(payload);
      console.log("USER", updated);

      return res.status(200).send({
        success: true,
        msg: "Event Updated successfully",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async approveRejectClass(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { class_id, type, reason, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (class_id == null || class_id == undefined)
      return requiredParams(res, "class_id is required!", 406);
    if (type == null || type == undefined)
      return requiredParams(res, "type is required!", 406);

    const isverified = `select count(*) as status from classes where class_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, class_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "class_id doesn't exists", 406);
    }

    try {
      const updated = await approveRejectClassService(payload);
      console.log("USER", updated);

      return res.status(200).send({
        success: true,
        msg: "Class Updated successfully",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  // static async approveRejectReported(req, res) {
  //   const payload = req.body;
  //   console.log("Payload:>>", payload);
  //   const { id, status, admin_id, type } = payload;

  //   if (admin_id == null || admin_id == undefined)
  //     return requiredParams(res, "admin_id is required", 406);
  //   if (id == null || id == undefined)
  //     return requiredParams(res, "business_id is required!", 406);
  //   if (type == null || type == undefined)
  //     return requiredParams(res, "type is required", 406);

  //   const isverified = `select count(*) as status from super_admin where admin_id = ?`;
  //   const [[verifiedcheck]] = await db.promise().query(isverified, admin_id);
  //   console.log("verified Check:>>", verifiedcheck);

  //   if (verifiedcheck.status === 0) {
  //     return requiredParams(res, "admin_id doesn't exists", 406);
  //   }

  //   try {
  //     const [[result]] = await approveRejectReportedService(payload);
  //     console.log("Reported details", result);
  //     if (result) {
  //       //title=`${result.p_title} `
  //       const notification = await getFirebaseNotification(
  //         result.v_user_id,
  //         1,
  //         null,
  //         result.v_title,
  //         result.v_msg
  //       );
  //       console.log(notification);
  //     }

  //     return res.status(200).send({
  //       success: true,
  //       msg: "Reported details fetched successfully",
  //       data: result,
  //     });
  //     // } else {
  //     //   return res.status(500).send({
  //     //     success: true,
  //     //     msg: "Internal Server Error",
  //     //     data: {},
  //     //   });
  //     // }
  //   } catch (error) {
  //     console.log("error", error);
  //     return res.status(200).send({
  //       success: false,
  //       msg: error.message || `Something went wrong`,
  //       data: {},
  //     });
  //   }
  // }

  static async approveRejectReported(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { id, status, admin_id, type } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (id == null || id == undefined)
      return requiredParams(res, "business_id is required!", 406);
    if (type == null || type == undefined)
      return requiredParams(res, "type is required", 406);

    const isverified = `select count(*) as status from super_admin where admin_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, admin_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "admin_id doesn't exists", 406);
    }

    try {
      const [result] = await approveRejectReportedService(payload);

      console.log("result return by procedure is ", result);

      const notification = await getFirebaseNotification(
        result.user_id,
        result.title,
        result.msg,
        type
      );
      console.log("Reported details", result);

      return res.status(200).send({
        success: true,
        msg: "Reported details fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async approveRejectReportedParent(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    const isverified = `select count(*) as status from super_admin where admin_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, admin_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "admin_id doesn't exists", 406);
    }

    const isverifiedparent = `select count(*) as status from Parents where parent_id = ?`;
    const [[verifiedcheckparent]] = await db
      .promise()
      .query(isverifiedparent, parent_id);
    console.log("verified Check:>>", verifiedcheckparent);

    if (verifiedcheckparent.status === 0) {
      return requiredParams(res, "parent_id doesn't exists", 406);
    }

    try {
      const [[result]] = await approveRejectReportedParentService(payload);
      console.log("Reported details", result);
      return res.status(200).send({
        success: true,
        msg: "Reported details fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async disableAccount(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { id, type, status } = payload;

    if (id == null || id == undefined)
      return requiredParams(res, "id is required!", 406);

    if (type == null || type == undefined)
      return requiredParams(res, "type is required!", 406);

    if (status == null || status == undefined)
      return requiredParams(res, "status is required!", 406);

    const isverified = `select count(*) as status from Users where parent_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "parent_id is required!", 406);
    }

    let result;
    try {
      // console.log("User already exist!", isExist.user_id);
      const updated = await disableAccountService(payload);
      console.log("updateOtp", updated);

      return res.status(200).send({
        success: true,
        msg: "Account status updated successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async getBusinessList(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }

      console.log("export", payload.action);
      if (payload.action === "export") {
        const result = await exportBusinessListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await getBusinessListService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "Business data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async home(req, res) {
    try {
      const payload = req;
      const [result] = await homeService();
      console.log("result", result);
      return res.status(200).send({
        success: true,
        msg: "Home page details fetched successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getRolesList(req, res) {
    try {
      const payload = req;
      const [result] = await getRolesListService();
      console.log("result", result);
      return res.status(200).send({
        success: true,
        msg: "Roles list fetched successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async setAdminRoles(req, res) {
    const payload = req.body;
    console.log("Payload:", payload);

    const { admin_id } = payload;

    if (admin_id == null || admin_id === undefined) {
      return requiredParams(res, "admin_id is required", 406);
    }

    // Verify admin_id existence
    const isverified = `SELECT COUNT(*) AS status FROM super_admin WHERE admin_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, [admin_id]);
    console.log("Verified Check:", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "admin_id doesn't exist", 406);
    }
    try {
      const result = await setAdminRolesService(payload);
      console.log("Result:::", result);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Admin role added successfully",
          data: {},
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: "Admin role not updated or set",
          data: {},
        });
      }
    } catch (error) {
      console.error("Error:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async kycList(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }
      console.log("export", payload.action);
      if (payload.action === "export") {
        const result = await exportKycListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await kycListService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "Kyc data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async adminList(req, res) {
    try {
      const [userData] = await adminListService();
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "Admin List fetched successfully",
        data: userData,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async eventList(req, res) {
    try {
      console.log("i am inside eventList function");

      // Retrieve admin_id from request body
      const admin_id = req.body.admin_id;
      console.log("Admin_id:", admin_id);

      // Validate admin_id presence
      if (!admin_id) {
        return requiredParams(res, "admin_id is required!", 406);
      }

      // Check if admin_id exists in super_admin table
      const checkSQL = `SELECT COUNT(*) AS count FROM super_admin WHERE admin_id = ?`;
      const [[{ count }]] = await db.promise().query(checkSQL, [admin_id]);
      console.log("Query result:", count);

      // If admin_id does not exist, return appropriate response
      if (count < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }

      // Call service to fetch event data using payload
      const payload = req.body;
      if (payload.action === "export") {
        const result = await exportEventListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await eventListService(payload);
        console.log("User data:", userData);

        // Return success response with fetched event data
        return res.status(200).send({
          success: true,
          msg: "Event List data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("Error in eventList function:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async reportList(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      const type = req.body.type;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      if (type == null || type == undefined)
        return requiredParams(res, "type is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }
      if (payload.action === "export") {
        const result = await exportReportListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await reportListService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "report List data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async parentsList(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      const type = req.body.type;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }

      if (payload.action === "export") {
        const result = await exportParentListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await parentsListService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "parents List data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async parentById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    const isverified = `select count(*) as status from Parents where parent_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, parent_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "parent_id doesn't exists", 406);
    }

    try {
      const [[result]] = await getParentByIdService(payload);
      console.log("parents details", result);

      return res.status(200).send({
        success: true,
        msg: "Parents details fetched successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getAdminById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);

    const isverified = `select count(*) as status from super_admin where admin_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, admin_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "admin_id doesn't exists", 406);
    }

    try {
      const [[result]] = await getAdminByIdService(payload);
      console.log("Admin details", result);

      return res.status(200).send({
        success: true,
        msg: "Admin details fetched successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async editAdminById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { admin_id } = payload;

    if (admin_id == null || admin_id === undefined) {
      return requiredParams(res, "admin_id is required", 406);
    }

    const isverified = `SELECT COUNT(*) AS status FROM super_admin WHERE admin_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, [admin_id]);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "admin_id doesn't exist", 406);
    }

    try {
      const [[result]] = await editAdminByIdService(payload);
      console.log("Admin details", result);

      return res.status(200).send({
        success: true,
        msg: "Admin details updated successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async deleteAdminById(req, res) {
    const { admin_id } = req.body;

    if (!admin_id) {
      console.log("Missing admin_id");
      return res.status(400).send({
        success: false,
        msg: "admin_id is required",
        data: {},
      });
    }

    const isverified = `SELECT COUNT(*) AS status FROM super_admin WHERE admin_id = ?`;

    try {
      console.log("Checking if admin_id exists...");
      const [[verifiedcheck]] = await db
        .promise()
        .query(isverified, [admin_id]);
      console.log("Verified check result:", verifiedcheck);

      if (verifiedcheck.status === 0) {
        console.log("admin_id does not exist");
        return res.status(404).send({
          success: false,
          msg: "admin_id doesn't exist",
          data: {},
        });
      }

      console.log("Deleting admin...");
      const result = await deleteAdminByIdService(admin_id);
      console.log("Deletion result:", result);

      return res.status(200).send({
        success: true,
        msg: "Admin details deleted successfully",
        data: {},
      });
    } catch (error) {
      console.error("Error occurred:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async setHomepageImage(req, res) {
    const payload = req.body;
    console.log("Payload:", payload);

    const { admin_id, id, type } = payload;

    if (admin_id == null || admin_id === undefined) {
      return requiredParams(res, "admin_id is required", 406);
    }
    if (type == null || type === undefined) {
      return requiredParams(res, "type is required", 406);
    }

    // Verify admin_id existence
    const isverified = `SELECT COUNT(*) AS status FROM super_admin WHERE admin_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, [admin_id]);
    console.log("Verified Check:", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "admin_id doesn't exist", 406);
    }
    try {
      const result = await setHomepageImageService(payload);
      console.log("Image details", result);

      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Homepage images updated successfully",
          data: [],
        });
      } else {
        return res.status(404).send({
          success: false,
          msg: "Homepage images not found or updated",
          data: {},
        });
      }
    } catch (error) {
      console.error("Error:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }

  static async reportedParentslist(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      const type = req.body.type;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }
      if (payload.action === "export") {
        const result = await exportReportedParentsListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await reportedParentsListService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "Reorted parents List data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async inactiveParents(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      const type = req.body.type;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }
      if (payload.action === "export") {
        const result = await exportInactiveParentsService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await inactiveParentsService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "Inactive parents List data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error message", error);
    }
  }

  static async classList(req, res) {
    try {
      console.log("i am inside it");
      const admin_id = req.body.admin_id;
      console.log("Admin_id", admin_id);
      // console.log("req.params.id", req.params.id);
      if (admin_id == null || admin_id == undefined)
        return requiredParams(res, "admin_id is required!", 406);

      const payload = req.body;
      const checkSQL = `select count(*) as status from super_admin where admin_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [admin_id]); // Make sure to pass admin_id as an array
      console.log("isExist", isExist.status);

      if (isExist.status < 1) {
        return res.status(403).send({
          success: false,
          msg: "admin_id does not exist",
          data: {},
        });
      }
      if (payload.action === "export") {
        const result = await exportClassListService(payload);
        console.log("result", result);
        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Data Exported",
            data: result,
          });
        } else {
          return res.status(200).send({
            success: false,
            msg: "Failed to export data ",
            data: {},
          });
        }
      } else {
        const [userData, meta] = await classListService(payload);
        console.log("User data::>>", userData);

        return res.status(200).send({
          success: true,
          msg: "Class List data fetched successfully",
          data: userData,
          meta: meta,
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getBusinessById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id, status, admin_id, type } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);
    if (type == null || type == undefined)
      return requiredParams(res, "type is required", 406);

    const isverified = `select count(*) as status from business where business_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, business_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "business_id doesn't exists", 406);
    }

    try {
      const [[result]] = await getBusinessByIdService(payload);
      console.log("business details", result);

      return res.status(200).send({
        success: true,
        msg: "Business details fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getBusinessLocation(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);

    const isverified = `select count(*) as status from business where business_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, business_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "business_id doesn't exists", 406);
    }

    try {
      const [result] = await getBusinessLocationService(payload);
      console.log("business locations", result);

      return res.status(200).send({
        success: true,
        msg: "Business Locations fetched successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async reportedEventById(req, res) {
    const payload = req.body;
    console.log("Payload::>>", payload);
    const { admin_id, event_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);

    if (event_id == null || event_id == undefined)
      return requiredParams(res, "event_id is required", 406);

    const isexist = `select count(*) as status from super_admin where admin_id = ?`;
    const [[existcheck]] = await db.promise().query(isexist, admin_id);
    console.log("existCheck", existcheck);

    if (existcheck.status === 0) {
      return requiredParams(res, "admin doesn't exists", 406);
    }
    const iseventreported = `select count(*) as status from reportedevents where event_id = ?`;
    const [[reportedCheck]] = await db
      .promise()
      .query(iseventreported, event_id);
    console.log("reported Check::>>", reportedCheck);

    if (reportedCheck.status === 0) {
      return requiredParams(
        res,
        "requested event_id is not being reported",
        406
      );
    }

    try {
      const [[result]] = await reportedEventByIdService(payload);
      console.log("result", result);

      return res.status(200).send({
        success: true,
        msg: " Reported event detail fetched successfully ",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async reportedClassById(req, res) {
    const payload = req.body;
    console.log("Payload::>>", payload);
    const { admin_id, class_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);

    if (class_id == null || class_id == undefined)
      return requiredParams(res, "class_id is required", 406);

    const isexist = `select count(*) as status from super_admin where admin_id = ?`;
    const [[existcheck]] = await db.promise().query(isexist, admin_id);
    console.log("existCheck", existcheck);

    if (existcheck.status === 0) {
      return requiredParams(res, "admin doesn't exists", 406);
    }
    const isclassreported = `select count(*) as status from reportedclasses where class_id = ?`;
    const [[reportedCheck]] = await db
      .promise()
      .query(isclassreported, class_id);
    console.log("reported Check::>>", reportedCheck);

    if (reportedCheck.status === 0) {
      return requiredParams(
        res,
        "requested class_id is not being reported",
        406
      );
    }

    try {
      const [[result]] = await reportedClassByIdService(payload);
      console.log("result", result);

      return res.status(200).send({
        success: true,
        msg: " Reported class detail fetched successfully ",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async eventRequestById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { request_id, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (request_id == null || request_id == undefined)
      return requiredParams(res, "request_id is required!", 406);

    const isverified = `select count(*) as status from event_requests where request_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, request_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "request_id doesn't exists", 406);
    }

    try {
      const [[result]] = await eventRequestByIdService(payload);
      console.log("business details", result);

      return res.status(200).send({
        success: true,
        msg: "Event detail fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async classRequestById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { request_id, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    if (request_id == null || request_id == undefined)
      return requiredParams(res, "request_id is required!", 406);

    const isverified = `select count(*) as status from class_requests where request_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, request_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "request_id doesn't exists", 406);
    }

    try {
      const [[result]] = await classRequestByIdService(payload);
      console.log("business details", result);

      return res.status(200).send({
        success: true,
        msg: "Class detail fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async kycRequestById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { request_id, admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);

    if (request_id == null || request_id == undefined)
      return requiredParams(res, "request_id is required", 406);

    const isverified = `select count(*) as status from kyc_requests where request_id = ?`;
    const [[verifiedcheck]] = await db.promise().query(isverified, request_id);
    console.log("verified Check:>>", verifiedcheck);

    if (verifiedcheck.status === 0) {
      return requiredParams(res, "request_id doesn't exists", 406);
    }

    try {
      const [[result]] = await kycRequestByIdService(payload);
      console.log("business details", result);

      return res.status(200).send({
        success: true,
        msg: "kyc_details details fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async analytics(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id, status, admin_id, type } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);
    // if (business_id == null || business_id == undefined)
    //   return requiredParams(res, "business_id is required!", 406);
    // if (type == null || type == undefined)
    //   return requiredParams(res, "type is required", 406);

    // const isverified = `select count(*) as status from business where business_id = ?`;
    // const [[verifiedcheck]] = await db.promise().query(isverified, business_id);
    // console.log("verified Check:>>", verifiedcheck);

    // if (verifiedcheck.status === 0) {
    //   return requiredParams(res, "business_id doesn't exists", 406);
    // }

    try {
      const [[result]] = await analyticsService(payload);
      console.log("business details", result);

      return res.status(200).send({
        success: true,
        msg: "Analytics fetched successfully",
        data: result,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async analyticsModule(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { admin_id } = payload;

    if (admin_id == null || admin_id == undefined)
      return requiredParams(res, "admin_id is required", 406);

    try {
      const [[result]] = await analyticsModuleService(payload);
      console.log("ANALYTICS details", result);

      return res.status(200).send({
        success: true,
        msg: "Analytics fetched successfully",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
}

module.exports = SuperAdminController;
