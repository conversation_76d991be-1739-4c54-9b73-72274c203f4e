import mysql from "mysql2/promise";

// Original hardcoded config (commented for reference)
// const db = mysql.createPool({
//   host: "staging-database.c9gyiq0ycydr.ap-south-1.rds.amazonaws.com",
//   user: "admin",
//   password: "Accessgranted",
//   database: "parenthingcomplete_uat",
// });

// New environment-based config
const db = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
});

export default db;
