// /**
//  * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
//  */
// exports.handler = async (event) => {
//     console.log(`EVENT: ${JSON.stringify(event)}`);
//     return {
//         statusCode: 200,
//     //  Uncomment below to enable CORS requests
//     //  headers: {
//     //      "Access-Control-Allow-Origin": "*",
//     //      "Access-Control-Allow-Headers": "*"
//     //  },
//         body: JSON.stringify('Hello from Lambda!'),
//     };
// };

import NotificationsController from "./controller/NotificationsController.js";

export const handler = async (event) => {
  console.log("Lambda function triggered");

  // Determine which task to run based on the event
  const task = event.task;

  try {
    if (task === "eventNotification") {
      console.log("Running eventNotification task");
      await NotificationsController.getEventNotification();
    } else if (task === "profileUpdateReminderNotification") {
      console.log("Running profileUpdateReminderNotification task");
      await NotificationsController.getProfileUpdateReminderNotification();
    } else if (task === "profileCompleteReminderApp") {
      //App
      console.log("Running profileCompleteReminderApp task");
      await NotificationsController.sendProfileCompletionRemindersNotification();
    } else if (task === "sendEventReminderNotification") {
      console.log("Running sendEventReminderNotification task");
      await NotificationsController.sendEventRemindersNotification();
    } else if (task === "sendFreeEventNotification") {
      console.log("Running sendFreeEventNotification task");
      await NotificationsController.sendFreeEventNearbyUserLocationNotificaion();
    } else if (task === "NotifySimilarClasses") {
      console.log("Running NotifySimilarClasses task");
      await NotificationsController.notifySimilarClasses();
    } else if (task === "nearbyParentNotification") {
      console.log("Running nearbyParentNotification task");
      await NotificationsController.nearByParents();
    } else if (task === "sendPendingMatches") {
      console.log("Running sendPendingMatchesNotifications task");
      await NotificationsController.sendPendingMatchesNotifications();
    } else {
      console.log("Unknown task");
    }

    console.log("Job done successfully");
  } catch (error) {
    console.error("Error processing task:", error);
  }
};
