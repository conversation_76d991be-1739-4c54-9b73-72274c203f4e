import admin from "firebase-admin";

// Initialize Firebase Admin SDK
import serviceAccount from "../config/firebase.json" assert { type: "json" }; // Update with the correct path to your service account key file
//import db from "../db.js";
import db from "../config/db.js";
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

// Function to update the notification status in the database
export const getPendingNotifications = async () => {
  const sql = `SELECT * FROM notification_web WHERE is_close = 0`; // Adjust the query based on your schema
  const [rows] = await db.query(sql);
  return rows;
};

export const sendNotification = async (notification) => {
  const { user_id, title, msg, notification_type } = notification;
  const sql = `SELECT firebase_id AS fire_base_id FROM Users WHERE user_id = ?`;
  const [[row]] = await db.query(sql, user_id);

  if (!row.fire_base_id) {
    console.log("token is not found", row);
    return;
  }
  if (row && row.fire_base_id) {
    console.log("row", row);
    const message = {
      data: {
        title: String(title),
        body: String(msg),
        type: notification_type,
      },
      token: row.fire_base_id,
    };

    try {
      const response = await admin.messaging().send(message);
      console.log("Notification sent:", response);
      return response;
    } catch (error) {
      console.error("Error sending notification:", error);
      throw error;
    }
  } else {
    throw new Error("Firebase ID not found");
  }
};

export const updateNotificationStatus = async (notificationId, result) => {
  const sql = `UPDATE notification_web SET is_close = 1 WHERE id = ?`;
  await db.query(sql, [notificationId]);
};

// Function to send data payload as a notification
export const sendReminderNotification = async (user, message) => {
  console.log("sendReminderNotification running");

  const data = {
    title: "Profile Update Reminder",
    body: message,
    notification_type: "reminder", // Example type, adjust as needed
  };

  try {
    const messagePayload = {
      data: data,
      apns: {
        headers: {
          "apns-priority": "10",
        },
        payload: {
          aps: {
            badge: 0,
            sound: "default",
            alert: {
              title: data.title,
              body: data.body,
            },
          },
          title: data.title,
          body: data.body,
          type: data.notification_type,
        },
        fcm_options: {
          image:
            "https://parenthing.s3.ap-south-1.amazonaws.com/dummy/banner.png",
        },
      },
      token: user.token,
    };

    console.log("messagePayload", messagePayload);
    const msg = await admin.messaging().send(messagePayload);
    console.log("messge send to ", user);

    console.log("Successfully sent message:", msg);

    return msg;
  } catch (error) {
    console.log("Error sending message:", error);
  }
};

//App service:

export const getEventsHappeningToday = async () => {
  const [[rows]] = await db.query("CALL spGetEventsHappeningToday()");
  return rows;
};

export const getEventsHappeningTomorrow = async () => {
  const [[rows]] = await db.query("CALL spGetEventsHappeningTomorrow()");
  return rows;
};

export const getFirebaseNotification = async (
  user_id,
  title,
  message,
  type
) => {
  const sql = `SELECT firebase_id AS fire_base_id FROM Users WHERE user_id = ?`;
  const [[user]] = await db.query(sql, user_id);

  if (!user.fire_base_id) {
    console.log("token is not found", user);
    return;
  }
  if (user && user.fire_base_id) {
    console.log("user", user);
  }
  console.log("sendReminderNotification running");

  const data = {
    title: title,
    body: message,
    notification_type: type, // Example type, adjust as needed
  };

  try {
    const messagePayload = {
      data: data,
      apns: {
        headers: {
          "apns-priority": "10",
        },
        payload: {
          aps: {
            badge: 0,
            sound: "default",
            alert: {
              title: data.title,
              body: data.body,
            },
          },
          title: data.title,
          body: data.body,
          type: data.notification_type,
        },
        fcm_options: {
          image:
            "https://parenthing.s3.ap-south-1.amazonaws.com/dummy/banner.png",
        },
      },
      token: user.fire_base_id,
    };

    console.log("messagePayload", messagePayload);
    const msg = await admin.messaging().send(messagePayload);
    console.log("messge send to ", user);

    console.log("Successfully sent message:", msg);

    return msg;
  } catch (error) {
    console.log("Error sending message:", error);
  }
};
export const updateNotificationTable = async (
  user_id,
  connection_id,
  role_id,
  title,
  msg,
  notification_type
) => {
  try {
    const [result] = await db.query(
      "CALL spInsertNotification(?, ?, ?, ?, ?, ?)",
      [user_id, connection_id, role_id, title, msg, notification_type]
    );
    return result;
  } catch (error) {
    console.error("Error inserting notification:", error);
    throw error;
  }
};

// for profile incomplete
export const profileCompleteReminderService = async () => {
  try {
    // Fetch parents from the database
    const [parents] = await db.query(
      `SELECT parent_id FROM Parents WHERE is_deleted = 0 AND is_disabled = 0`
    );

    const notificationsSent = [];

    for (const parent of parents) {
      const parentId = parent.parent_id;

      // Call stored procedure to check profile completeness
      const [result] = await db.query(
        `CALL spIsProfileComplete(JSON_OBJECT('parent_id', ?))`,
        [parentId]
      );
      console.log("not completed users", result);
      const { user_id, first_name, status } = result[0][0];

      if (status === 0) {
        const messageTitle = "Complete Your Profile!";
        const msg = `${first_name}  Complete your profile today to get even better recommendations.`;
        const type = "user";
        console.log(messageTitle, msg, type);
        // Send notification using your service
        const notification = await getFirebaseNotification(
          user_id,
          messageTitle,
          msg,
          type
        );

        if (notification) {
          // Insert a record in the notification table if needed
          try {
            await updateNotificationTable(
              user_id,
              null,
              1,
              messageTitle,
              msg,
              type
            );
            console.log("Notification record inserted successfully");
          } catch (error) {
            console.error("Error inserting notification record:", error);
          }
        }
      }
    }

    return notificationsSent;
  } catch (error) {
    console.error("Error processing profile reminders:", error);
    throw error;
  }
};

// for free notification in user locality
export const getFreeEventNearbyLocation = async (latitude, longitude) => {
  try {
    // Call the stored procedure with the given parameters
    const [rows] = await db.query(
      "CALL parenthingcomplete_uat.spGetNearbyFreeEvents(?, ?, ?)",
      [latitude, longitude, 10.0]
    );

    // Return the result of the stored procedure
    console.log("number of notification are ", rows.length);
    return rows.length;
  } catch (error) {
    console.error("Error fetching nearby free events:", error);
    throw error;
  }
};

export const findAndNotifyMatchingClassesService = async (
  className,
  parentId
) => {
  try {
    console.log(`Calling stored procedure with parentId: ${parentId}`);

    // Call the stored procedure
    const [[rows]] = await db.query(
      "CALL parenthingcomplete_uat.spFindSimilarClasses(?)",
      [parentId]
    );

    // console.log(`Stored procedure returned rows: ${JSON.stringify(rows)}`);
    // Check if rows are returned
    if (!rows || rows.length === 0 || !rows[0] || rows[0].length === 0) {
      console.log("No matching classes found.");
      return;
    }
    // Loop through the results and prepare notifications
    // for (const row of rows[0]) {
    //   const matchingClassName = row.matching_class_name;

    //   // Prepare notification details
    //   const title = `We found more ${matchingClassName} matching classes for you`;
    //   const body = `Looking for ${matchingClassName}? We've got you!`;

    //   // Send notification using your existing method
    //   await getFirebaseNotification(parentId, title, body);
    // }
    return rows;
  } catch (error) {
    console.error("Error finding and notifying similar classes:", error);
    throw error;
  }
};

// near by parents
export const getNearbyParentLocation = async (latitude, longitude) => {
  try {
    // Call the stored procedure with the given parameters
    const [rows] = await db.query(
      "CALL parenthingcomplete_uat.spGetNearbyParents(?, ?, ?)",
      [latitude, longitude, 10.0]
    );

    // Return the result of the stored procedure
    console.log("number of Parens are ", rows.length - 1);
    return rows.length - 1;
  } catch (error) {
    console.error("Error fetching nearby free events:", error);
    throw error;
  }
};
