const db = require("../config/db");

const { requiredParams } = require("../utility/requiredCheck");

const {
  getClassListService,
  getClassByIdService,
  createClassService,
  deleteClassbyIdService,
} = require("../service/ClassService");

class ClassController {
  static async getClassList(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { parent_id, city } = payload;

    if (parent_id == null || parent_id == undefined)
      return requiredParams(res, "parent_id is required!", 406);

    // if (city == null || city == undefined)
    //   return requiredParams(res, "city is required", 406);

    // const checkSQL = `SELECT count(*) as status from Serviceable where city = ?`;
    // const [[isExist]] = await db.promise().query(checkSQL, [payload.city]);
    // console.log("isExist::>>", isExist);
    // if (
    //   isExist.status === 0 ||
    //   isExist.status == null ||
    //   isExist.status == undefined
    // ) {
    //   return res.status(404).send({
    //     success: false,
    //     msg: "Not in serviceable city list",
    //     data: {},
    //   });
    // }

    const checkSQL = `SELECT count(*) as status from Parents where parent_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.parent_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested Parent ID is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const data = await getClassListService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class List",
        data: data,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async getClassById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { class_id } = payload;

    if (class_id == null || class_id == undefined)
      return requiredParams(res, "class_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from classes where class_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.class_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested class ID is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const [[data]] = await getClassByIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class Details",
        data: data,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async createClass(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);

    try {
      // console.log("User already exist!", isExist.user_id);
      const [data] = await createClassService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class created successfully",
        data: data,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async deleteClassbyId(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);

    try {
      // console.log("User already exist!", isExist.user_id);
      const [data] = await deleteClassbyIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class deleted successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
}

module.exports = ClassController;
