const jwt = require("jsonwebtoken");

const secretKey = "81cdaacb-888a-4e37-94ae-b7065ad7ddw3";

const authorization = (req, res, next) => {
  const authHeader = req.headers["authorization"];
  console.log("Req. headers", req.headers);
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: "Authorization header is missing",
      data: {},
    });
  }

  const tokenParts = authHeader.split(" ");

  if (tokenParts.length !== 2 || tokenParts[0] !== "Bearer") {
    return res.status(401).json({
      success: false,
      message: "Invalid Authorization header format",
      data: {},
    });
  }

  const token = tokenParts[1];
  if (!token) {
    return res
      .status(401)
      .json({ success: false, message: "Token is required", data: {} });
  }

  jwt.verify(token, secretKey, (err, decoded) => {
    if (err) {
      return res
        .status(401)
        .json({ success: false, message: "Invalid token", data: {} });
    }
    console.log("Decoded", decoded);
    // req.user = decoded;
    next();
  });
};

module.exports = authorization;
