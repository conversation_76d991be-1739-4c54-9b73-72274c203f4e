const mysql = require("mysql2");

// Original hardcoded config (commented for reference)
// let config = {
//   host: "staging-database.c9gyiq0ycydr.ap-south-1.rds.amazonaws.com",
//   user: "admin",
//   password: "Accessgranted",
//   database: "parenthingcomplete_uat",
// };

// New environment-based config
let config = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
};

const connection = mysql.createPool(config);

module.exports = connection;
