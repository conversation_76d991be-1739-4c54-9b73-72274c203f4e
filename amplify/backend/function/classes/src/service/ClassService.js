const db = require("../config/db");

const getClassListService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spClassesList(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getClassByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spClassbyId(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const createClassService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spCreateClass(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const deleteClassbyIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spCreateClass(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

module.exports = {
  getClassListService,
  getClassByIdService,
  createClassService,
  deleteClassbyIdService,
};
