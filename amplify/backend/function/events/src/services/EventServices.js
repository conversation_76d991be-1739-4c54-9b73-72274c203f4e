const db = require("../config/db");

const getEventListService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEventsList(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    console.log("Row::>>", row);
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const getEventByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spEventById(?)`;
    const [row] = await db.promise().query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const createEventService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spCreateEvent(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

const deleteEventByIdService = async (payload) => {
  try {
    console.log("Payload in verifyOtp::>>", payload);
    const procedure = `CALL spDeleteEventById(?)`;
    const [[row]] = await db
      .promise()
      .query(procedure, JSON.stringify(payload));
    if (row) {
      return row;
    } else {
      return null;
    }
  } catch (error) {
    console.log("Error in app", error);
  }
};

module.exports = {
  getEventListService,
  getEventByIdService,
  createEventService,
  deleteEventByIdService,
};
