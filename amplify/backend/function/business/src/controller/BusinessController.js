const db = require("../config/db");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { requiredParams } = require("../utility/requiredCheck");
const axios = require("axios");
const uuid = require("uuid");
const secretKey = "81cdaacb-888a-4e37-94ae-b7065ad7ddw3";

const {
  getEventByBusinessIdService,
  categoryListService,
  editClassByIdService,
  classListService,
  getClassByIdService,
  deleteEventByIdService,
  getEventByIdService,
  deleteClassbyIdService,
  createEventService,
  editEventByIdService,
  kycService,
  sendOtpService,
  getAdminDetailsService,
  getMSG91Service,
  createClassService,
  addLocationService,
  getDetailsService,
  verifiedMSG91Service,
  verifyOtpService,
  getUserDetailService,
  getBusinessListService,
  getUserByIdService,
  editProfileService,
  getHomepageImagesService,
  registerBusinessService,
  editLocationService,
  totalClassesEventsService,
  deleteLocationService,
  kycdetailsService,
  saveDeviceTokenWebService,
  logOutWebService,
  getNotificationListService,
} = require("../service/BusinessService");

const keygenerator = () => {
  // Generate a unique license key using UUID
  const licenseKey = uuid.v4();

  // Construct the license object
  const license = {
    key: licenseKey,
  };

  return license;
};
class BusinessController {
  static async getLocationByLatLong(req, res) {
    const { lat, lng, map_api_key } = req.body;
    if (!lat || !lng) {
      return res.status(400).json("lat and lng are required!");
    }
    if (!map_api_key) {
      return res.status(400).json("map_api_key is required!");
    }

    try {
      console.log("Making API call to Google Maps...");
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${map_api_key}`
      );
      const data = await response.json();
      console.log("Google Maps API response:", data);

      if (!response.ok) {
        return res.status(response.status).json({
          success: false,
          msg: data.error_message || "Failed to fetch location.",
          data: {},
        });
      }

      if (!data.results || data.results.length === 0) {
        return res.status(404).json({
          success: false,
          msg: "No results found for the given coordinates.",
          data: {},
        });
      }

      return res.status(200).send({
        success: true,
        msg: "Fetched successfully.",
        data: data.results, // Return just the results
      });
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getLocationByAddress(req, res) {
    const { input, sessionToken, map_api_key, place_id } = req.body;

    // if (!input) {
    //   return res.status(400).json("Input is required!");
    // }
    if (!map_api_key) {
      return res.status(400).json("map_api_key is required!");
    }

    try {
      console.log("Making API call to Google Maps for address autocomplete...");
      let response;
      if (place_id) {
        // response = await fetch(
        //   `https://maps.googleapis.com/maps/api/place/details/json?place_id=${place_id}&key=${map_api_key}&sessiontoken=${sessionToken}`
        // );

        // Place Details request with language parameter
        response = await fetch(
          `https://maps.googleapis.com/maps/api/place/details/json?` +
          `place_id=${place_id}` +
          `&key=${map_api_key}` +
          `&sessiontoken=${sessionToken}` +
          `&language=en`
        );       

        const data = await response.json();
        console.log("Google Maps API response:", data?.result);

        return res.status(200).send({
          success: true,
          msg: "Fetched successfully.",
          data: data?.result ? data?.result : data, // Return just the predictions
        });
      } else {
        // response = await fetch(
        //   `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${input}&key=${map_api_key}&sessiontoken=${sessionToken}&components=country:IN`
        // );

        const data = await response.json();
        console.log("Google Maps API response:", data);

        if (!response.ok) {
          return res.status(response.status).json({
            success: false,
            msg: data.error_message || "Failed to fetch location.",
            data: {},
          });
        }

        if (!data.predictions || data.predictions.length === 0) {
          return res.status(404).json({
            success: false,
            msg: "No results found for the given input.",
            data: {},
          });
        }

        return res.status(200).send({
          success: true,
          msg: "Fetched successfully.",
          data: data.predictions, // Return just the predictions
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async saveDeviceTokenWeb(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);

    const { business_id } = payload;

    // Validate required parameters
    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);
    // if (user_firebase_token == null || user_firebase_token == undefined)
    //   return requiredParams(res, "user_firebase_token is required!", 406);

    try {
      // Check if the business exists
      const checkSQL = `SELECT COUNT(*) AS status FROM Users WHERE business_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, business_id);

      if (isExist.status != 0) {
        // Call the service to save the device token
        const [result] = await saveDeviceTokenWebService(payload);

        if (result) {
          return res.status(200).send({
            success: true,
            msg: "Device Token Saved!",
            data: {},
          });
        } else {
          return res.status(500).send({
            success: false,
            msg: "Failed to save the device token.",
            data: {},
          });
        }
      } else {
        return res.status(400).send({
          success: false,
          msg: "Business does not exist!",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: [],
      });
    }
  }

  static async sendOtp(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { mobile, name } = payload;
    const otp = Math.floor(1000 + Math.random() * 9000).toString();
    console.log("otp", otp);
    // const encryptedOtp = otp;
    const encryptedOtp = await bcrypt.hash(otp, 10);
    console.log("EncryptedOTP", encryptedOtp);
    // const userPayload = {
    //   ...payload,
    //   otp: encryptedOtp,
    //   user_type: "Business",
    // };
    if (mobile == null || mobile == undefined)
      return requiredParams(res, "Mobile number is required!", 406);

    if (!/^\d{10}$/.test(mobile)) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number must be 10 digits long",
        data: {},
      });
    }

    // Check if the mobile number is registered
    const checkMobile = `SELECT COUNT(*) AS count,user_type FROM OTP WHERE mobile = ? AND (user_type = 'Business' or user_type='SuperAdmin') `;
    const [[mobileCheck]] = await db.promise().query(checkMobile, mobile);
    console.log("mobileCheck", mobileCheck);

    if (mobileCheck.count === 0) {
      return res.status(400).send({
        success: false,
        msg: "Mobile number is not registered",
        data: {},
      });
    }
    const userPayload = {
      ...payload,
      otp: encryptedOtp,
      user_type:
        mobileCheck?.user_type === "SuperAdmin" ? "SuperAdmin" : "Business",
    };

    const isadmin = `select isverified from OTP where mobile = ? AND user_type = 'SuperAdmin'`;
    const [[adminCheck]] = await db.promise().query(isadmin, mobile);
    console.log("adminCheck:>>", adminCheck);
    if (adminCheck && adminCheck.isverified === 1) {
      // await verifiedMSG91Service({
      //   mobile: mobile,
      //   otp: otp,
      // });
    }

    const isverified = `select isverified from OTP where mobile = ? AND user_type = 'Business'`;
    const [[verifiedcheck]] = await db.promise().query(isverified, mobile);
    console.log("verified Check:>>", verifiedcheck);
    let result;
    try {
      if (verifiedcheck && verifiedcheck.isverified === 1) {
        // await verifiedMSG91Service({
        //   mobile: mobile,
        //   otp: otp,
        // });
      } else if (
        verifiedcheck?.isverified == null ||
        verifiedcheck?.isverified == undefined ||
        verifiedcheck?.isverified == 0
      ) {
        console.log("else field");
        // await getMSG91Service({
        //   mobile: mobile,
        //   otp: otp,
        // });
      }

      // console.log("User already exist!", isExist.user_id);
      const updated = await sendOtpService(userPayload);
      console.log("updateOtp", updated);

      return res.status(200).send({
        success: true,
        msg: "Otp sent successfully.",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async verifyOtp(req, res) {
    try {
      const payload = req.body;
      console.log("payload ", payload);
      const { otp, mobile } = payload;

      // Check if mobile and otp are provided
      if (!mobile)
        return requiredParams(res, "Mobile number is required!", 406);
      if (!otp) return requiredParams(res, "OTP is required!", 406);

      // Validate mobile number format
      if (!/^\d{10}$/.test(mobile)) {
        return res.status(400).send({
          success: false,
          msg: "Mobile number must be 10 digits long",
          data: {},
        });
      }

      // Check if the account is disabled
      const [[activecheck]] = await db
        .promise()
        .query(
          `SELECT isactive FROM Users WHERE mobile = ? AND (role_id = 2 OR role_id=3)`,
          [mobile]
        );
      console.log("activecheck", activecheck);
      if (activecheck && activecheck.isactive === 0) {
        return res.status(403).send({
          success: false,
          msg: "Your account is disabled",
          data: {},
        });
      }

      // Check for SuperAdmin
      const [[adminExist]] = await db
        .promise()
        .query(
          `SELECT count(*) as status, otp_id as temp_id, sentotp as otp, business_id, admin_id, isverified, mobile, updated_at, DATE_ADD(updated_at, INTERVAL 1 MINUTE) as expire_time FROM OTP WHERE mobile = ? AND user_type = 'SuperAdmin'`,
          [mobile]
        );
      console.log("isExist Admin", adminExist);
      if (adminExist.isverified === 1 && otp === "0000") {
        const adminData = {
          admin_id: adminExist.admin_id,
        };
        const [[userData]] = await getAdminDetailsService(adminData);
        console.log("Userdata::>>", userData);

        const random_key = await keygenerator();
        const token = jwt.sign({ random_key }, secretKey, {
          expiresIn: "30d",
        });
        const userDatadetails = {
          ...userData,
          token: token,
          isprofileComplete: 1,
        };
        return res.status(200).send({
          success: true,
          msg: "Login Successful",
          data: userDatadetails,
        });
      }

      // Check for Business
      const [[isExist]] = await db
        .promise()
        .query(
          `SELECT count(*) as status, otp_id as temp_id, sentotp as otp, business_id, isverified, mobile, updated_at, DATE_ADD(updated_at, INTERVAL 1 MINUTE) as expire_time FROM OTP WHERE mobile = ? AND user_type = 'Business'`,
          [mobile]
        );
      console.log("isExist user", isExist);
      const comparison = await bcrypt.compare(otp, isExist.otp);
      console.log("comparison", comparison);
      if (isExist.isverified === 1 && (comparison || otp === "0000")) {
        const businessData = {
          business_id: isExist.business_id,
        };
        const [[userData]] = await getDetailsService(businessData);
        console.log("Userdata::>>", userData);
        const proc = `CALL spIsBusinessProfileComplete(?)`;
        const [[[row]]] = await db
          .promise()
          .query(proc, JSON.stringify(businessData));
        console.log("spIsBusinessProfileComplete", row.status);
        const isComplete = row.status;
        const random_key = await keygenerator();
        const token = jwt.sign({ random_key }, secretKey, {
          expiresIn: "30d",
        });
        const userDatadetails = {
          ...userData,
          token: token,
          isprofileComplete: isComplete,
        };
        return res.status(200).send({
          success: true,
          msg: "Login Successful",
          data: userDatadetails,
        });
      } else if (comparison === true || otp === "0000") {
        console.log("payload before verify::>>", payload);
        const [user] = await verifyOtpService(payload);
        console.log("user", user);
        const [result] = await getUserDetailService({
          business_id: user.business_id,
          type: "newuser",
        });
        const data = { ...result, is_newuser: user.is_newuser };
        console.log("result", result);
        return res.status(200).send({
          success: true,
          msg: "User verified successfully.",
          data: data,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "OTP incorrect.",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async logOutWeb(req, res) {
    const payload = req.body;
    const { business_id } = payload;
    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from Users where business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.business_id]);

    try {
      if (isExist.status > 0) {
        const updated = await logOutWebService(payload);
        console.log("logOut", updated);

        return res.status(200).send({
          success: true,
          msg: "firebase_id deleted successfully.",
          data: {},
        });
      } else {
        return res.status(500).send({
          success: true,
          msg: "business_id does not exists",
          data: {},
        });
      }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getEventByBusinessId(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id } = payload;

    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return res.status(406).send({
        success: false,
        msg: `business_id is required`,
        data: [],
      });
    }
    if (
      !payload.hasOwnProperty("type") ||
      payload.type === null ||
      payload.type === undefined
    ) {
      return res.status(406).send({
        success: false,
        msg: `type is required`,
        data: [],
      });
    }

    if (
      payload.type !== "published" &&
      payload.type !== "draft" &&
      payload.type !== "past"
    ) {
      return res.status(406).send({
        success: false,
        msg: `type must be either "published", "draft" or "past"`,
        data: [],
      });
    }

    const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.business_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested business_id is not available",
        data: {},
      });
    }
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const [userData, meta] = await getEventByBusinessIdService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "Event Lists fetched successfully",
        data: userData,
        meta: meta,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async totalClassesEvents(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id } = payload;

    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return res.status(406).send({
        success: false,
        msg: `business_id is required`,
        data: [],
      });
    }

    const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.business_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested business_id is not available",
        data: {},
      });
    }
    try {
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const [[userData]] = await totalClassesEventsService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "Event Lists fetched successfully",
        data: userData,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async createEvent(req, res) {
    try {
      const payload = req.body;
      const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
      const [[isExist]] = await db
        .promise()
        .query(checkSQL, [req.body.business_id]);
      console.log("isExist::>>", isExist);
      if (isExist.status === 0) {
        return res.status(404).send({
          success: false,
          msg: "Requested business_id is not available",
          data: {},
        });
      }

      const [userData] = await createEventService(payload);
      console.log("check2");
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "Event created successfully",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async createClass(req, res) {
    const payload = req.body;
    const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [req.body.business_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested business_id is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const [data] = await createClassService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class created successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async editEventById(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    if (
      !payload.hasOwnProperty("event_id") ||
      payload.event_id === null ||
      payload.event_id === undefined
    ) {
      return requiredParams(res, "event_id is required!", 406);
    }

    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "business_id is required!", 406);
    }
    try {
      const checkSQL = `SELECT count(*) as status from events where event_id = ?`;
      const [[isExist]] = await db
        .promise()
        .query(checkSQL, [payload.event_id]);
      console.log("isExist::>>", isExist);
      if (isExist.status > 0) {
        const data = await editEventByIdService(payload);
        console.log("Data:>>", data);

        // const [[userData]] = await getDetailsService(payload);

        return res.status(200).send({
          success: true,
          msg: "Event edited successfully",
          data: {},
        });
      } else {
        return res.status(500).send({
          success: true,
          msg: "Event does not exists",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async editClassById(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    if (
      !payload.hasOwnProperty("class_id") ||
      payload.class_id === null ||
      payload.class_id === undefined
    ) {
      return requiredParams(res, "class_id is required!", 406);
    }

    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "business_id is required!", 406);
    }
    try {
      const checkSQL = `SELECT count(*) as status from classes where class_id = ?`;
      const [[isExist]] = await db
        .promise()
        .query(checkSQL, [payload.class_id]);
      console.log("isExist::>>", isExist);
      if (isExist.status > 0) {
        const data = await editClassByIdService(payload);
        console.log("Data:>>", data);
        return res.status(200).send({
          success: true,
          msg: "Class edited successfully",
          data: {},
        });
      } else {
        return res.status(200).send({
          success: true,
          msg: "class does not exists",
          data: {},
        });
      }
    } catch (error) {
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async getBusinessList(req, res) {
    try {
      console.log("inside business 1");
      const payload = req.body;
      console.log("inside business 2");
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);
      const [userData, meta] = await getBusinessListService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "Business data fetched successfully",
        data: userData,
        meta: meta,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getHomepageImages(req, res) {
    const payload = req.body;
    console.log("Payload::>>", payload);

    try {
      console.log("Fetching homepage images");
      const images = await getHomepageImagesService(payload);
      console.log("Homepage images::>>", images);

      return res.status(200).send({
        success: true,
        msg: "Homepage images fetched successfully",
        data: images,
      });
    } catch (error) {
      console.log("Error fetching homepage images", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async registerBusiness(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // Required fields validation
    const requiredFields = ["business_name", "business_type", "business_phone"];

    for (const field of requiredFields) {
      if (!payload.hasOwnProperty(field) || !payload[field]) {
        return requiredParams(res, `${field} is required!`, 406);
      }
    }

    try {
      const data = await registerBusinessService(payload);

      if (data) {
        return res.status(200).send({
          success: true,
          msg: "Business registration is successful",
          data: data,
        });
      } else {
        return res.status(500).send({
          success: false,
          msg: "Error registering business",
          data: {},
        });
      }
    } catch (error) {
      console.error("Error::>>", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Internal server error",
        data: error.message,
      });
    }
  }
  static async addLocation(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // Required fields validation
    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "business_id is required!", 406);
    }

    const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.business_id]);
    console.log("isExist::>>", isExist);

    if (isExist.status > 0) {
      try {
        const data = await addLocationService(payload);

        if (data) {
          return res.status(200).send({
            success: true,
            msg: "Location added successfully",
            data: {},
          });
        } else {
          return res.status(500).send({
            success: false,
            msg: "Error while adding location",
            data: {},
          });
        }
      } catch (error) {
        console.error("Error::>>", error);
        return res.status(500).send({
          success: false,
          msg: "Error while adding location",
          data: error.message,
        });
      }
    } else {
      return res.status(500).send({
        success: false,
        msg: "Business does not exist",
        data: {},
      });
    }
  }

  static async editLocation(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // Required fields validation
    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "business_id is required!", 406);
    }

    if (
      !payload.hasOwnProperty("location_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "location_id is required!", 406);
    }

    const checkSQL = `SELECT count(*) as status from Locations where location_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.location_id]);
    console.log("isExist::>>", isExist);

    if (isExist.status > 0) {
      try {
        const data = await editLocationService(payload);

        if (data) {
          return res.status(200).send({
            success: true,
            msg: "Location updated successfully",
            data: {},
          });
        } else {
          return res.status(500).send({
            success: false,
            msg: "Error while updating location",
            data: {},
          });
        }
      } catch (error) {
        console.error("Error::>>", error);
        return res.status(500).send({
          success: false,
          msg: "Error while updating location",
          data: error.message,
        });
      }
    } else {
      return res.status(500).send({
        success: false,
        msg: "Location does not exist",
        data: {},
      });
    }
  }

  static async deleteLocationbyId(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // Required fields validation
    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "business_id is required!", 406);
    }

    if (
      !payload.hasOwnProperty("location_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "location_id is required!", 406);
    }

    const checkSQL = `SELECT count(*) as status from Locations where location_id = ? AND business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.location_id, payload.business_id]);
    console.log("isExist::>>", isExist);

    if (isExist.status > 0) {
      try {
        const data = await deleteLocationService(payload);

        if (data) {
          return res.status(200).send({
            success: true,
            msg: "Location deleted successfully",
            data: {},
          });
        } else {
          return res.status(500).send({
            success: false,
            msg: "Error while deleting location",
            data: {},
          });
        }
      } catch (error) {
        console.error("Error::>>", error);
        return res.status(500).send({
          success: false,
          msg: "Error while deleting location",
          data: error.message,
        });
      }
    } else {
      return res.status(500).send({
        success: false,
        msg: "Location does not exist",
        data: {},
      });
    }
  }

  static async classList(req, res) {
    try {
      console.log("inside business 1");
      const payload = req.body;
      console.log("inside business 2");

      if (
        !payload.hasOwnProperty("business_id") ||
        payload.business_id === null ||
        payload.business_id === undefined
      ) {
        return res.status(406).send({
          success: false,
          msg: `business_id is required`,
          data: [],
        });
      }
      if (
        !payload.hasOwnProperty("type") ||
        payload.type === null ||
        payload.type === undefined
      ) {
        return res.status(406).send({
          success: false,
          msg: `type is required`,
          data: [],
        });
      }

      if (payload.type !== "published" && payload.type !== "draft") {
        return res.status(406).send({
          success: false,
          msg: `type must be either "published" or "draft"`,
          data: [],
        });
      }
      const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
      const [[isExist]] = await db
        .promise()
        .query(checkSQL, [payload?.business_id]);
      console.log("isExist::>>", isExist);
      if (isExist.status === 0) {
        return res.status(404).send({
          success: false,
          msg: "Requested business_id is not available",
          data: {},
        });
      }

      // console.log("User already exist!", isExist.user_id);
      const [userData, meta] = await classListService(payload);
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "Business data fetched successfully",
        data: userData,
        meta: meta,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async categoryList(req, res) {
    try {
      const [result] = await categoryListService();
      console.log("Result:", result);
      if (result) {
        return res.status(200).send({
          success: true,
          msg: "Category List",
          data: result,
        });
      } else {
        return res.status(200).send({
          success: false,
          msg: "No categories found.",
          data: [],
        });
      }
    } catch (error) {
      console.log("Error in categoryList:", error);
      return res.status(500).send({
        success: false,
        msg: error.message || "Something went wrong",
        data: {},
      });
    }
  }
  static async getEventById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { event_id } = payload;

    if (event_id == null || event_id == undefined)
      return requiredParams(res, "event_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from events where event_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.event_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested event ID is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const [[data]] = await getEventByIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Event Details",
        data: data,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getClassById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { class_id } = payload;

    if (class_id == null || class_id == undefined)
      return requiredParams(res, "class_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from classes where class_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [payload.class_id]);
    console.log("isExist::>>", isExist);
    if (
      isExist.status === 0 ||
      isExist.status == null ||
      isExist.status == undefined
    ) {
      return res.status(404).send({
        success: false,
        msg: "Requested class ID is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const [[data]] = await getClassByIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class Details",
        data: data,
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async deleteClassById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { class_id } = req.body;
    try {
      if (class_id == null || class_id == undefined)
        return requiredParams(res, "class_id is required!", 406);

      const checkSQL = `SELECT count(*) as status from classes where class_id = ?`;
      const [[isExist]] = await db
        .promise()
        .query(checkSQL, [payload.class_id]);
      console.log("isExist::>>", isExist);
      if (
        isExist.status === 0 ||
        isExist.status == null ||
        isExist.status == undefined
      ) {
        return res.status(404).send({
          success: false,
          msg: "Requested class ID is not available",
          data: {},
        });
      }

      // console.log("User already exist!", isExist.user_id);
      const [data] = await deleteClassbyIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Class deleted successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async deleteEventById(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { event_id } = req.body;
    try {
      if (event_id == null || event_id == undefined)
        return requiredParams(res, "event_id is required!", 406);

      const checkSQL = `SELECT count(*) as status from events where event_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [event_id]);
      console.log("isExist::>>", isExist);
      if (
        isExist.status === 0 ||
        isExist.status == null ||
        isExist.status == undefined
      ) {
        return res.status(404).send({
          success: false,
          msg: "Requested event ID is not available",
          data: {},
        });
      }

      // console.log("User already exist!", isExist.user_id);
      const [data] = await deleteEventByIdService(payload);
      //   console.log("updateOtp", updated);
      console.log("data::>>", data);
      return res.status(200).send({
        success: true,
        msg: "Event deleted successfully",
        data: {},
      });
      // } else {
      //   return res.status(500).send({
      //     success: true,
      //     msg: "Internal Server Error",
      //     data: {},
      //   });
      // }
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async editProfile(req, res) {
    try {
      const payload = req.body;
      // const payload = req.body;
      // if (
      //   verifiedcheck?.isverified == null ||
      //   verifiedcheck?.isverified == undefined ||
      //   verifiedcheck?.isverified == 0
      // )
      //  {

      // console.log("User already exist!", isExist.user_id);

      const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
      const [[isExist]] = await db
        .promise()
        .query(checkSQL, [req.body.business_id]);
      console.log("isExist::>>", isExist);
      if (isExist.status === 0) {
        return res.status(404).send({
          success: false,
          msg: "Requested business_id is not available",
          data: {},
        });
      }

      const userData = await editProfileService(payload);
      console.log("check2");
      console.log("User data::>>", userData);

      return res.status(200).send({
        success: true,
        msg: "business profile edited successfully",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(200).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }
  static async kyc(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const {
      business_id,
      business_name,
      business_address,
      document_type,
      document_number,
      document_url,
    } = payload;

    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);

    if (business_name == null || business_name == undefined)
      return requiredParams(res, "business_name is required!", 406);
    if (business_address == null || business_address == undefined)
      return requiredParams(res, "business_address is required!", 406);
    if (document_type == null || document_type == undefined)
      return requiredParams(res, "document_type is required!", 406);
    if (document_number == null || document_number == undefined)
      return requiredParams(res, "document_number is required!", 406);
    if (document_url == null || document_url == undefined)
      return requiredParams(res, "document_url is required!", 406);

    const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [business_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested business_id is not available",
        data: {},
      });
    }

    const checkKYC = `SELECT COUNT(*) AS status
    FROM business AS b
    LEFT JOIN kyc_requests AS k ON b.business_id = k.business_id
    WHERE b.business_id = ? AND k.status != 'rejected';`;
    const [[kycExist]] = await db.promise().query(checkKYC, [business_id]);
    console.log("isExist::>>", kycExist);
    if (kycExist.status > 0) {
      return res.status(409).send({
        success: false,
        msg: "Duplicate KYC entry detected. The provided details already exist in our records.",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const result = await kycService(payload);
      console.log("Result", result);

      return res.status(200).send({
        success: true,
        msg: "KYC form submitted successfully.",
        data: {},
      });
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getKycDetails(req, res) {
    const payload = req.body;
    console.log("Payload:>>", payload);
    const { business_id } = payload;

    if (business_id == null || business_id == undefined)
      return requiredParams(res, "business_id is required!", 406);

    const checkSQL = `SELECT count(*) as status from business where business_id = ?`;
    const [[isExist]] = await db.promise().query(checkSQL, [business_id]);
    console.log("isExist::>>", isExist);
    if (isExist.status === 0) {
      return res.status(404).send({
        success: false,
        msg: "Requested business_id is not available",
        data: {},
      });
    }

    try {
      // console.log("User already exist!", isExist.user_id);
      const [[result]] = await kycdetailsService(payload);
      console.log("Result", result);

      return res.status(200).send({
        success: true,
        msg: "KYC details fetched successfully.",
        data: result,
      });
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getUserById(req, res) {
    try {
      console.log("Request params Id", req.params.business_id);
      const business_id = req.params.business_id;

      // Check if the business exists
      const checkSQL = `SELECT count(*) as status FROM business WHERE business_id = ?`;
      const [[isExist]] = await db.promise().query(checkSQL, [business_id]);
      console.log("isExist::>>", isExist);

      if (isExist.status === 0) {
        return res.status(404).send({
          success: false,
          msg: "Requested business_id is not available",
          data: {},
        });
      }

      // Fetch business details
      const payload = { business_id: business_id };
      const [[userData]] = await getUserByIdService(payload);
      console.log("User data::>>", userData);

      // Fetch locations for the business
      const locationsSQL = `SELECT * FROM Locations WHERE business_id = ?`;
      const [locations] = await db.promise().query(locationsSQL, [business_id]);

      // Fetch profile completion status
      const proc = `CALL spIsProfileCompleteWeb(?)`;
      const [[[row]]] = await db
        .promise()
        .query(proc, [JSON.stringify(payload)]);
      console.log("spIsProfileComplete", row.profile_completion_percentage);
      const isComplete = row.profile_completion_percentage;

      // Create response without location_details
      const { location_details, ...businessDetails } = userData; // Exclude location_details

      // Nest locations inside business details
      const responseData = {
        ...businessDetails,
        locations: locations, // Add locations array
        isprofileComplete: isComplete,
      };

      // Prepare response
      return res.status(200).send({
        success: true,
        msg: "User data fetched successfully",
        data: {
          business: responseData, // Include nested structure
        },
      });
    } catch (error) {
      console.log("error", error);
      return res.status(500).send({
        // Use 500 for server errors
        success: false,
        msg: error.message || `Something went wrong`,
        data: {},
      });
    }
  }

  static async getNotificationList(req, res) {
    const payload = req.body;

    console.log("Payload::>>", payload);

    // Required fields validation
    if (
      !payload.hasOwnProperty("business_id") ||
      payload.business_id === null ||
      payload.business_id === undefined
    ) {
      return requiredParams(res, "business_id is required!", 406);
    }

    const checkSQL = `SELECT count(*) as status from Users where business_id = ?`;
    const [[isExist]] = await db
      .promise()
      .query(checkSQL, [payload.business_id]);
    console.log("isExist::>>", isExist);

    if (isExist.status > 0) {
      try {
        const [data] = await getNotificationListService(payload);
        console.log("DATA", data);

        if (data) {
          return res.status(200).send({
            success: true,
            msg: "Notification list fetched successfully",
            data: data,
          });
        } else {
          return res.status(500).send({
            success: false,
            msg: "Error while fetching List",
            data: {},
          });
        }
      } catch (error) {
        console.error("Error::>>", error);
        return res.status(500).send({
          success: false,
          msg: error.message || `Something went wrong`,
          data: error.message,
        });
      }
    } else {
      return res.status(500).send({
        success: false,
        msg: "Business does not exist",
        data: {},
      });
    }
  }
}

module.exports = BusinessController;
